<template>
  <view class="fee-poup">
    <u-popup
      v-model="show"
      mode="center"
      width="650rpx"
      height="80%"
      border-radius="14"
      z-index="9999"
      :closeable="false"
      close-icon-color="#58bc58"
      class="fee-poup"
    >
      <view class="content-wrapper">
        <image class="image" mode="widthFix" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/yjjy.jpg"></image>
      </view>
      <view class="close" @click="closePoup">
        <image class="image" src="@/static/toc/close.png"></image
      ></view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      modal: {
        show: false,
        close: false,
        align: "center",
        showCancel: false,
        showConfirm: false
      },
      thStyle: {
        width: "200rpx"
      },
      thStyle2: {
        width: "300rpx"
      },
      show: false,
    };
  },
  methods: {
    openPoup() {
      // this.$refs.popup.open("center");
      this.modal.show = true;
      this.show = true;
    },
    closePoup() {
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.fee-poup {
  z-index: 9999;
  // position: relative;
  .content-wrapper {
    // width: 600rpx;
    // height: 1100rpx;
    // overflow-x: scroll;
    padding: 28rpx;
    padding-top: 48rpx;
    position: relative;
    .title {
      margin-bottom: 20rpx;
      text-align: center;
      font-size: 28rpx;
      font-weight: 600;
    }
    .title-1 {
      margin-bottom: 10rpx;
      font-weight: 400;
    }
    .title-2 {
      margin-bottom: 30rpx;
      font-weight: 400;
    }
  }
}
.close {
  position: fixed;
  width: 35rpx;
  height: 35rpx;
  top: 9%;
  background: #fff;
  border-radius: 50%;
  right: 43rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
</style>