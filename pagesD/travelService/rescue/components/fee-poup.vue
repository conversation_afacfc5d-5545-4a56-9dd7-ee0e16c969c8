<template>
  <view class="fee-poup">
    <u-popup
      v-model="show"
      mode="center"
      width="650rpx"
      height="80%"
      border-radius="14"
      z-index="9999"
      :closeable="false"
      close-icon-color="#58bc58"
      class="fee-poup"
    >
      <view class="content-wrapper">
        <view class="title">广西高速公路交通排障、拯救作业收费标准表</view>
        <view class="title-1">一、拖车(硬拖)收费标准</view>

        <u-table :th-style="thStyle" bg-color="#fff">
          <view class="table-container" id="tableContainer">
            <view class="simulated-v-thumb" :style="vThumbStyleString"></view>
            <scroll-view
              scroll-x="true"
              :show-scrollbar="false"
              class="horizontal-scroll"
              id="hScrollView"
              style="width: 100%; position: relative;"
              @scroll="onHorizontalScroll($event, 'simulatedHThumbStyle', 'hScrollViewWidth')"
            >
              <u-tr
                id="tableHeader"
                style="position: sticky;top:0; background-color: #fff; z-index: 1;"
              >
                <u-th>车型</u-th>
                <u-th>车型备注</u-th>
                <u-th>车况</u-th>
                <u-th>拖车5公里收费标准（元）</u-th>
                <u-th>拖车10公里收费标准（元）</u-th>
                <u-th>拖车15公里收费标准（元）</u-th>
                <u-th>拖车20公里收费标准（元）</u-th>
                <u-th>拖车25公里收费标准（元）</u-th>
                <u-th>拖车30公里收费标准（元）</u-th>
                <u-th>拖车35公里收费标准（元）</u-th>
                <u-th>拖车40公里收费标准（元）</u-th>
                <u-th>拖车45公里收费标准（元）</u-th>
                <u-th>拖车50公里收费标准（元）</u-th>
                <u-th>拖车55公里收费标准（元）</u-th>
              </u-tr>

              <scroll-view
                scroll-y="true"
                v-if="huocheList.length > 0"
                :show-scrollbar="false"
                class="vertical-scroll"
                id="vScrollView"
                style="width: 2780rpx;box-sizing: border-box;height:500rpx; /* No margin-bottom here */"
                @scroll="onVerticalScroll"
              >
                <u-tr v-for="(item, idx) in huocheList" :key="idx">
                  <u-td style="background-color: #f5f6f8;">{{
                    item.faultyVehicleTypeText
                  }}</u-td>
                  <u-td>{{ item.vehicleDesc }}</u-td>
                  <u-td>{{ item.faultyVehicleConditionText }}</u-td>
                  <u-td>{{ item.fiveKmToll }}</u-td>
                  <u-td>{{ item.tenKmToll }}</u-td>
                  <u-td>{{ item.fifteenKmToll }}</u-td>
                  <u-td>{{ item.twentyKmToll }}</u-td>
                  <u-td>{{ item.twentyFiveKmToll }}</u-td>
                  <u-td>{{ item.thirtyKmToll }}</u-td>
                  <u-td>{{ item.thirtyFiveKmToll }}</u-td>
                  <u-td>{{ item.fortyKmToll }}</u-td>
                  <u-td>{{ item.fortyFiveKmToll }}</u-td>
                  <u-td>{{ item.fiftyKmToll }}</u-td>
                  <u-td>-</u-td>
                </u-tr>
              </scroll-view>
              <view v-else class="empty-data-placeholder" :style="{height: viewDimensions.vScrollViewHeight + 'px'}"> </view>
            </scroll-view>
            <view class="simulated-h-thumb" :style="hThumbStyleString"></view>
          </view>
        </u-table>
        <view class="title-1">备注说明:</view>
        <view class="title-1"
          >1、本标准以拯救车拖车里程5公里为基本计算单位(起步价)，5公里以内按5公里计算。5公里以上每增加5公里按收费表对应增加收费;增加的里程尾数不足5公里的部分按增加5公里计算。</view
        >
        <view class="title-2">2、收费金额按四舍五入取整至元。</view>

        <view class="title-1">二、吊车收费标准</view>
        <view class="footer-table-container">
          <scroll-view
            scroll-x="true"
            :show-scrollbar="false"
            class="horizontal-scroll-footer"
            id="hScrollView2"
            style="width: 100%;margin-bottom: 10rpx;"
            @scroll="onHorizontalScroll($event, 'simulatedH2ThumbStyle', 'hScrollView2Width')"
          >
            <u-table :th-style="thStyle2" bg-color="#fff">
              <u-tr>
                <u-th>吊车车型</u-th>
                <u-th>车型备注</u-th>
                <u-th>出车费（元/台）</u-th>
                <u-th>台班费（元/台）</u-th>
              </u-tr>
              <u-tr v-for="(item, idx) in diaocheList" :key="idx">
                <u-td style="background-color: #f5f6f8;">{{
                  item.craneTypeText
                }}</u-td>
                <u-td>{{ item.description }}</u-td>
                <u-td>{{ item.departureFee }}</u-td>
                <u-td>{{ item.shiftFee }}</u-td>
              </u-tr>
            </u-table>
          </scroll-view>
          <view class="simulated-h-thumb" :style="h2ThumbStyleString"></view>
        </view>
        <view class="title-1"
          >备注:吊车费由出车费和台班费组成，台班费的时间从吊车在事故地开始作业算起至将事故车吊好为止</view
        >
      </view>
      <view class="close" @click="closePoup">
        <image class="image" src="@/static/toc/close.png"></image
      ></view>
    </u-popup>
  </view>
</template>

<script>
// Helper function to convert camelCase to kebab-case for CSS properties
function camelToKebab(str) {
  return str.replace(/([A-Z])/g, (g) => `-${g[0].toLowerCase()}`);
}

// NEW: pxToRpx helper and screenWidth
const screenWidth = uni.getSystemInfoSync().windowWidth;
function pxToRpx(px) {
  if (screenWidth === 0 || !px) return 0; // Avoid division by zero and handle undefined/null/0px
  return parseFloat(((px / screenWidth) * 750).toFixed(2)); // Return as number, round to 2 decimal places
}

const MIN_THUMB_SIZE_RPX_CONST = 40; // rpx, define as a local const for init

export default {
  data() {
    const MAX_THUMB_PROPORTION_CONST = 0.40;

    return {
      // 弹窗显示控制
      show: false,
      
      // 表格样式配置
      thStyle: {
        width: "200rpx"
      },
      thStyle2: {
        width: "300rpx"
      },
      
      // 表格数据
      huocheList: [], // 拖车收费标准列表
      diaocheList: [], // 吊车收费标准列表
      // 视图尺寸信息（存储px值）
      viewDimensions: {
        vScrollViewHeight: uni.upx2px(500), // 纵向滚动视图高度
        hScrollViewWidth: uni.upx2px(600),  // 横向滚动视图宽度
        hScrollView2Width: uni.upx2px(600), // 第二个横向滚动视图宽度
        headerHeight: uni.upx2px(48)        // 表头高度
      },
      
      // 滚动条配置常量
      MIN_THUMB_SIZE_RPX: MIN_THUMB_SIZE_RPX_CONST,    // 滚动条最小尺寸（rpx）
      MAX_THUMB_PROPORTION: MAX_THUMB_PROPORTION_CONST, // 滚动条最大比例
      HORIZONTAL_SAFETY_MARGIN_RPX: 120,               // 横向滚动条安全边距（rpx）
      VERTICAL_SCROLL_SNAP_THRESHOLD_RPX: 5,           // 纵向滚动吸附阈值（rpx）
      HORIZONTAL_SCROLL_SNAP_THRESHOLD_RPX: 5,         // 横向滚动吸附阈值（rpx）

      // 模拟纵向滚动条样式
      simulatedVThumbStyle: {
        position: "absolute",
        right: "3rpx",
        width: pxToRpx(3) + "rpx",
        backgroundColor: "#c1c1c1",
        borderRadius: "1.5rpx",
        zIndex: 99,
        pointerEvents: "none",
        transition: "top 0.02s, height 0.02s",
        opacity: 0.8,
        top: pxToRpx(uni.upx2px(48)) + "rpx",
        height: MIN_THUMB_SIZE_RPX_CONST + "rpx"
      },
      // 模拟第一个横向滚动条样式
      simulatedHThumbStyle: {
        position: "absolute",
        bottom: "0rpx",
        height: pxToRpx(3) + "rpx",
        backgroundColor: "#c1c1c1",
        borderRadius: "1.5rpx",
        zIndex: 99,
        pointerEvents: "none",
        transition: "left 0.02s, width 0.02s",
        opacity: 0.8,
        left: "0rpx",
        width: MIN_THUMB_SIZE_RPX_CONST + "rpx"
      },
      // 模拟第二个横向滚动条样式
      simulatedH2ThumbStyle: {
        position: "absolute",
        bottom: "0rpx",
        height: pxToRpx(3) + "rpx",
        backgroundColor: "#c1c1c1",
        borderRadius: "1.5rpx",
        zIndex: 99,
        pointerEvents: "none",
        transition: "left 0.02s, width 0.02s",
        opacity: 0.8,
        left: "0rpx",
        width: MIN_THUMB_SIZE_RPX_CONST + "rpx"
      }
    };
  },
  computed: {
    // 将纵向滚动条样式对象转换为CSS字符串
    vThumbStyleString() {
      let styleString = '';
      for (const key in this.simulatedVThumbStyle) {
        styleString += `${camelToKebab(key)}:${this.simulatedVThumbStyle[key]};`;
      }
      return styleString;
    },
    // 将第一个横向滚动条样式对象转换为CSS字符串
    hThumbStyleString() {
      let styleString = '';
      for (const key in this.simulatedHThumbStyle) {
        styleString += `${camelToKebab(key)}:${this.simulatedHThumbStyle[key]};`;
      }
      return styleString;
    },
    // 将第二个横向滚动条样式对象转换为CSS字符串
    h2ThumbStyleString() {
      let styleString = '';
      for (const key in this.simulatedH2ThumbStyle) {
        styleString += `${camelToKebab(key)}:${this.simulatedH2ThumbStyle[key]};`;
      }
      return styleString;
    }
  },
  methods: {
    // 打开弹窗
    openPoup() {
      this.show = true;
      this.$nextTick(async () => {
        await this.getDimensions(); // 获取视图尺寸
        this.setInitialThumbState(); // 设置滚动条初始状态
      });
    },
    // 关闭弹窗
    closePoup() {
      this.show = false;
    },
    // 初始化数据和组件
    async init() {
      let params = {};
      try {
        // 获取拖车收费标准数据
        let res = await this.$request.post(this.$interfaces.getTrailerCharges, { data: params });
        this.huocheList = res.data.data || [];
        // 获取吊车收费标准数据
        let res2 = await this.$request.post(this.$interfaces.craneChargesList, { data: params });
        this.diaocheList = res2.data.data || [];
      } catch (error) {
        console.error("Error fetching data:", error);
      }
      await this.getDimensions(); // 获取视图尺寸
      this.setInitialThumbState(); // 设置滚动条初始状态
    },
    // 获取各个滚动视图的尺寸信息
    getDimensions() {
      return new Promise((resolve) => {
        const query = uni.createSelectorQuery().in(this);
        query.select('#vScrollView').boundingClientRect();
        query.select('#hScrollView').boundingClientRect();
        query.select('#hScrollView2').boundingClientRect();
        query.select('#tableHeader').boundingClientRect();
        query.exec(data => {
          // 获取纵向滚动视图高度
          if (data && data[0] && data[0].height > 0) { 
            this.viewDimensions.vScrollViewHeight = data[0].height;
          } else {
            console.warn('#vScrollView dimensions not found/invalid, using fallback (px):', this.viewDimensions.vScrollViewHeight);
          }
          // 获取第一个横向滚动视图宽度
          if (data && data[1] && data[1].width > 0) { 
            this.viewDimensions.hScrollViewWidth = data[1].width;
          } else {
            console.warn('#hScrollView dimensions not found/invalid, using fallback (px):', this.viewDimensions.hScrollViewWidth);
          }
          // 获取第二个横向滚动视图宽度
          if (data && data[2] && data[2].width > 0) {
            this.viewDimensions.hScrollView2Width = data[2].width;
          } else {
            console.warn('#hScrollView2 dimensions not found/invalid, using fallback (px):', this.viewDimensions.hScrollView2Width);
          }
          // 获取表头高度
          if (data && data[3] && data[3].height > 0) { 
            this.viewDimensions.headerHeight = data[3].height;
          } else {
            console.warn('#tableHeader dimensions not found/invalid, using fallback (px):', this.viewDimensions.headerHeight);
          }
          resolve();
        });
      });
    },
    // 设置滚动条初始状态
    setInitialThumbState(){
      const minThumbRpx = this.MIN_THUMB_SIZE_RPX;

      // 设置纵向滚动条初始位置和尺寸
      this.simulatedVThumbStyle.top = '0rpx';
      this.simulatedVThumbStyle.height = minThumbRpx + 'rpx';
      
      // 设置横向滚动条初始位置和尺寸
      this.simulatedHThumbStyle.left = '0rpx';
      this.simulatedHThumbStyle.width = minThumbRpx + 'rpx';
      this.simulatedH2ThumbStyle.width = minThumbRpx + 'rpx';

      // 初始化滚动条状态
      this.onVerticalScroll({ detail: { scrollTop: 0, scrollHeight: this.viewDimensions.vScrollViewHeight } });
      this.onHorizontalScroll(
        { detail: { scrollLeft: 0, scrollWidth: this.viewDimensions.hScrollViewWidth } },
        'simulatedHThumbStyle',
        'hScrollViewWidth'
      );
      this.onHorizontalScroll(
        { detail: { scrollLeft: 0, scrollWidth: this.viewDimensions.hScrollView2Width } },
        'simulatedH2ThumbStyle',
        'hScrollView2Width'
      );
    },
    // 处理纵向滚动事件，计算并更新纵向滚动条位置
    onVerticalScroll(e) {
      // 获取滚动位置和内容高度（px值）
      const scrollTopPx = e.detail.scrollTop;
      const scrollHeightPx = e.detail.scrollHeight;

      // 转换为rpx单位
      const scrollTopRpx = pxToRpx(scrollTopPx);
      const scrollHeightRpx = pxToRpx(scrollHeightPx);
      
      // 获取视图区域高度和最小滚动条尺寸
      const clientHeightRpx = pxToRpx(this.viewDimensions.vScrollViewHeight);
      const minThumbSizeRpx = this.MIN_THUMB_SIZE_RPX;

      // 如果视图高度无效，使用默认值
      if (this.viewDimensions.vScrollViewHeight <= 0) { 
        this.simulatedVThumbStyle.top = '0rpx';
        this.simulatedVThumbStyle.height = minThumbSizeRpx + 'rpx';
        return; 
      }

      let thumbHeightRpx, thumbActualTopRpx;
      // 如果内容高度不超过视图高度，无需滚动
      if (scrollHeightRpx <= clientHeightRpx) {
        thumbHeightRpx = minThumbSizeRpx; 
        thumbActualTopRpx = 0; 
      } else {
        // 计算滚动条高度（基于内容比例）
        const idealThumbHeightRpx = (clientHeightRpx / scrollHeightRpx) * clientHeightRpx;
        const proportionalThumbHeightCappedRpx = Math.min(idealThumbHeightRpx, clientHeightRpx * this.MAX_THUMB_PROPORTION);
        thumbHeightRpx = Math.max(minThumbSizeRpx, proportionalThumbHeightCappedRpx);
        thumbHeightRpx = Math.min(thumbHeightRpx, clientHeightRpx);
        
        // 计算滚动条可移动的最大距离
        const maxScrollTopRpx = scrollHeightRpx - clientHeightRpx;
        const maxThumbCanMoveRpx = Math.max(0, clientHeightRpx - thumbHeightRpx); 
        
        // 计算滚动条位置
        let thumbOffsetInTrackRpx = 0;
        if (maxScrollTopRpx > 0) { 
            let k_ratio_vertical = scrollTopRpx / maxScrollTopRpx;
            // 滚动到底部时的吸附效果
            if ((maxScrollTopRpx - scrollTopRpx) < this.VERTICAL_SCROLL_SNAP_THRESHOLD_RPX && scrollTopRpx > 0 && maxScrollTopRpx > 0) {
                 k_ratio_vertical = 1.0;
            }
            k_ratio_vertical = Math.min(k_ratio_vertical, 1.0);
            k_ratio_vertical = Math.max(k_ratio_vertical, 0.0); 
            thumbOffsetInTrackRpx = k_ratio_vertical * maxThumbCanMoveRpx;
        }
        thumbActualTopRpx = thumbOffsetInTrackRpx;
      }
      
      // 应用计算结果到滚动条样式（考虑表头高度偏移）
      const headerHeightRpx = pxToRpx(this.viewDimensions.headerHeight);
      this.simulatedVThumbStyle.height = parseFloat(thumbHeightRpx.toFixed(2)) + 'rpx';
      this.simulatedVThumbStyle.top = parseFloat((headerHeightRpx + thumbActualTopRpx).toFixed(2)) + 'rpx';
    },
    // 处理横向滚动事件，计算并更新横向滚动条位置
    onHorizontalScroll(
      e,
      thumbStyleKey = "simulatedHThumbStyle",
      scrollViewWidthKey = "hScrollViewWidth"
    ) {
      // 获取滚动位置和内容宽度（px值）
      const scrollLeftPx = e.detail.scrollLeft;
      const scrollWidthPx = e.detail.scrollWidth;

      // 转换为rpx单位
      const scrollLeftRpx = pxToRpx(scrollLeftPx);
      const scrollWidthRpx = pxToRpx(scrollWidthPx);

      // 获取相关尺寸和配置参数
      const clientWidthRpx = pxToRpx(this.viewDimensions[scrollViewWidthKey]);
      const minThumbSizeRpx = this.MIN_THUMB_SIZE_RPX;
      const horizontalSafetyMarginRpx = this.HORIZONTAL_SAFETY_MARGIN_RPX;
      const horizontalSnapThresholdRpx = this.HORIZONTAL_SCROLL_SNAP_THRESHOLD_RPX;

      if (this.viewDimensions[scrollViewWidthKey] <= 0) {
        this[thumbStyleKey].left = '0rpx';
        this[thumbStyleKey].width = minThumbSizeRpx + 'rpx';
        return; 
      }

      let thumbWidthRpx, thumbLeftOffsetRpx;
      if (scrollWidthRpx <= clientWidthRpx) {
        thumbWidthRpx = Math.min(minThumbSizeRpx, clientWidthRpx); 
        thumbLeftOffsetRpx = 0;
      } else {
        const idealThumbWidthRpx = (clientWidthRpx / scrollWidthRpx) * clientWidthRpx;
        const proportionalThumbWidthCappedRpx = Math.min(idealThumbWidthRpx, clientWidthRpx * this.MAX_THUMB_PROPORTION);
        thumbWidthRpx = Math.max(minThumbSizeRpx, proportionalThumbWidthCappedRpx);
        thumbWidthRpx = Math.min(thumbWidthRpx, clientWidthRpx); 

        const maxScrollLeftRpx = scrollWidthRpx - clientWidthRpx;
        const maxThumbCanMoveRpx = Math.max(0, clientWidthRpx - thumbWidthRpx - horizontalSafetyMarginRpx);

        thumbLeftOffsetRpx = 0;
        if (maxScrollLeftRpx > 0) { 
            let k_ratio_horizontal = scrollLeftRpx / maxScrollLeftRpx;
            if (
              maxScrollLeftRpx - scrollLeftRpx < horizontalSnapThresholdRpx &&
              scrollLeftRpx > 0 &&
              maxScrollLeftRpx > 0
            ) {
              k_ratio_horizontal = 1.0;
            }
            k_ratio_horizontal = Math.min(k_ratio_horizontal, 1.0);
            k_ratio_horizontal = Math.max(k_ratio_horizontal, 0.0);
            thumbLeftOffsetRpx = k_ratio_horizontal * maxThumbCanMoveRpx;
        }
      }
      
      this[thumbStyleKey].width = parseFloat(thumbWidthRpx.toFixed(2)) + 'rpx';
      this[thumbStyleKey].left = parseFloat(thumbLeftOffsetRpx.toFixed(2)) + 'rpx';
    }
  },
  created() {
    this.$nextTick(async () => {
      await this.init();
    });
  },
  mounted() {
  }
};
</script>

<style lang="scss" scoped>
.fee-poup {
  z-index: 9999;
  .content-wrapper {
    padding: 28rpx;
    padding-top: 48rpx;
    position: relative;
    .title {
      margin-bottom: 20rpx;
      text-align: center;
      font-size: 28rpx;
      font-weight: 600;
    }
    .title-1 {
      margin-bottom: 10rpx;
      font-weight: 400;
    }
    .title-2 {
      margin-bottom: 30rpx;
      font-weight: 400;
    }
  }
}
.close {
  position: fixed;
  width: 35rpx;
  height: 35rpx;
  top: 9%;
  background: #fff;
  border-radius: 50%;
  right: 43rpx;
  image {
    width: 100%;
    height: 100%;
  }
}

.table-container {
  position: relative;
  width: 100%;
  padding-bottom: 8px; /* Space for horizontal scrollbar thumb to be visible */
  overflow: hidden; /* 防止滚动条超出容器 */
}

.footer-table-container {
  position: relative;
  width: 100%;
  padding-bottom: 8px; /* Space for horizontal scrollbar thumb to be visible */
}

.empty-data-placeholder {
    width: 2780rpx; /* Match vertical-scroll width */
    box-sizing: border-box;
}

/* Styles for simulated thumbs are now fully managed by :style binding in <script> data via computed properties */
/* .simulated-v-thumb {} */
/* .simulated-h-thumb {} */

.horizontal-scroll,
.vertical-scroll,
.horizontal-scroll-footer {
  /* Hide native scrollbars */
  &::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE 10+ */
}

/* 添加最后一列的右边框样式 */
/deep/ .u-table .u-tr .u-th:last-child,
/deep/ .u-table .u-tr .u-td:last-child {
  border-right: 1px solid #e4e7ed !important;
}
</style>