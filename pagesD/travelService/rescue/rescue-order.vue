<template>
  <view class="msg-list">
    <u-tabs
      :list="tabList"
      :is-scroll="false"
      :current="current"
      activeColor="#0066E9"
      @change="change"
    >
    </u-tabs>
    <view class="scroll-box">
      <scroll-view
        scroll-y="true"
        class="scroll-Y"
        :lower-threshold="lowerThreshold"
        @scrolltolower="scrolltolower"
        @scroll="scroll"
      >
        <listItem
          v-for="(item, idx) in orderList"
          :key="idx"
          :itemInfo="item"
          :current="current"
        />
        <load-more :loadStatus="noticeLoadStatus" />
      </scroll-view>
    </view>

    <sideNav class="side" @toolTap="toolTap" />
    <tLoading :isShow="isLoading" />
  </view>
</template>

<script>
import listItem from "./components/list-item.vue";
import tLoading from "@/components/common/t-loading.vue";
import { getEtcAccountInfo,getLoginUserInfo } from "@/common/storageUtil.js";
import loadMore from "@/pagesD/components/load-more/index.vue";
import sideNav from "./components/side-nav.vue";

export default {
  components: {
    listItem,
    tLoading,
    sideNav,
    loadMore
  },
  data() {
    return {
      orderList: [],
      scrollTop: 0,
      noticeLoadStatus: 3,
      old: {
        scrollTop: 0
      },
      loadFlag: false,
      lowerThreshold: 120,
      isLoading: false,
      tabList: [
        {
          name: "救援",
          value: "0"
        },
        {
          name: "订单",
          value: "1"
        }
      ],
      orderTabList: [
        {
          name: "进行中",
          value: "0"
        },
        {
          name: "已完成",
          value: "1"
        }
      ],
      current: 1,
      orderCurrent: 0,
      page: 1,
      size: 10
    };
  },
  methods: {
    async getList(data) {
      console.log(data, "getLoaction");
      let params = {
        page: this.page,
        size: this.size,
        // userId: getEtcAccountInfo().custMastId,
        userId: getLoginUserInfo().userIdStr,
        status: this.orderCurrent
      };
      this.noticeLoadStatus = 1;
      this.loadFlag = true;
      let res = await this.$request.post(this.$interfaces.travelGetPageList, {
        data: params
      });
      console.log(res, "resss");
      if (res.code == 200) {
        if (res.data.data.records.length > 0) {
          this.noticeLoadStatus = 4;
          if (this.page == 1) {
            this.orderList = res.data.data.records;
          } else {
            this.orderList = this.orderList.concat(res.data.data.records);
          }
          this.text = ""; //重置错误信息
        } else {
          this.noticeLoadStatus = 3;
        }
      } else {
        if (this.page != 1) {
          this.noticeLoadStatus = 2;
        }
        // this.text = '抱歉，加载列表失败，请稍后重试'
      }
      // this.recommendAddress = result.formatted_addresses.recommend;
    },
    scrolltolower: function(e) {
      console.log("this.noticeLoadStatus ", this.noticeLoadStatus);
      if (this.noticeLoadStatus !== 4) return;
      let self = this;
      setTimeout(function() {
        self.page = self.page + 1;
        self.getList();
      }, 500);
    },
    scroll(e) {
      this.old.scrollTop = e.detail.scrollTop;
    },
    change(index) {
      if (index === 0) {
        // 切换到救援页面
        uni.navigateTo({
          url: "/pagesD/travelService/rescue/index"
        });
        return;
      }
      
      this.orderList = [];
      this.orderCurrent = index;
      this.page = 1;
      this.getList();
    },
    toolTap(item) {
      if (item.type == "rescueType") {
        uni.navigateTo({
          url: "/pagesD/travelService/rescue/index"
        });
      } else {

      }
    }
  },
  onLoad() {
    this.getList();
  }
};
</script>

<style lang="scss" scoped>
.msg-list {
  height: 100%;
  .scroll-box {
    padding: 20rpx;
  }
  .scroll-Y {
    height: calc(100vh - 100rpx);
  }
  .side {
    position: fixed;
    top: 780rpx;
    right: 0;
  }
}
</style>