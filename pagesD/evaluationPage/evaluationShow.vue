<template>
  <view class="evaluation-show-page">
    <view class="evaluation-content">
      <!-- 评价对象信息 -->
      <view class="business-info" v-if="evaluationData.businessInfo && evaluationData.businessInfo.name">
        <view class="business-name">{{evaluationData.businessInfo.name}}</view>
        <view class="business-detail" v-if="evaluationData.businessInfo.detail">{{evaluationData.businessInfo.detail}}</view>
        <view class="business-time" v-if="evaluationData.businessInfo.time">{{evaluationData.businessInfo.time}}</view>
      </view>
      
      <!-- 无评价数据时显示 -->
      <view class="no-evaluation" v-if="!hasEvaluation">
        <view class="no-evaluation-text">暂无评价~</view>
      </view>
      
      <!-- 有评价数据时显示 -->
      <block v-if="hasEvaluation">
        <!-- 评价内容展示 -->
        <view class="evaluation-box">
          <!-- 星级评价显示 -->
          <t-star-rating 
            :value="evaluationData.rate" 
            :readonly="true"
            :size="30"
          />
          
          <!-- 满意度文本 -->
          <view class="satisfaction-text" v-if="evaluationData.rate > 0">
            {{getSatisfactionText(evaluationData.rate)}}
          </view>
          
          <!-- 评价内容 -->
          <view class="evaluation-text" v-if="evaluationData.text">
            {{evaluationData.text}}
          </view>
          
          <!-- 评价标签 -->
          <t-evaluation-tags 
            v-if="evaluationData.tags && evaluationData.tags.length > 0"
            :tags="evaluationData.tags"
            :value="evaluationData.tags"
            :readonly="true"
          />
          
          <!-- 评价图片 -->
          <view class="uploaded-images" v-if="evaluationData.images && evaluationData.images.length > 0">
            <view class="uploaded-image" v-for="(item, index) in evaluationData.images" :key="index" @tap="previewImage(item)">
              <image :src="item" class="image-preview" mode="aspectFill"></image>
            </view>
          </view>
          
          <!-- 评价时间 -->
          <view class="evaluation-time" v-if="evaluationData.time">评价于: {{evaluationData.time}}</view>
          
          <!-- 添加回复内容 -->
          <view class="reply-container" v-if="evaluationData.reply">
            <view class="reply-header">
              <view class="reply-title">官方回复</view>
              <view class="reply-time" v-if="evaluationData.reply.time">{{evaluationData.reply.time}}</view>
            </view>
            <view class="reply-content">{{evaluationData.reply.content}}</view>
          </view>
        </view>
        
        <!-- 特定类型的评价详情 -->
        <block v-if="evaluationData.type">
          <!-- 服务区评价详情 -->
          <t-evaluation-items 
            v-if="evaluationType === 'serviceArea' && evaluationData.serviceAreaItems && evaluationData.serviceAreaItems.length > 0"
            title="服务区评价详情"
            :items="evaluationData.serviceAreaItems"
            :readonly="true"
          />
          
          <!-- 收费站评价详情 -->
          <t-evaluation-items 
            v-if="evaluationType === 'tollStation' && evaluationData.tollStationItems && evaluationData.tollStationItems.length > 0"
            title="收费站评价详情"
            :items="evaluationData.tollStationItems"
            :readonly="true"
          />
          
          <!-- 充电服务评价详情 -->
          <t-evaluation-items 
            v-if="evaluationType === 'chargingService' && evaluationData.chargingItems && evaluationData.chargingItems.length > 0"
            title="充电服务评价详情"
            :items="evaluationData.chargingItems"
            :readonly="true"
          />
        </block>
        
        <!-- 查看更多评论按钮 -->
        <view class="more-comments-btn" @tap="goToCommentList">
          <text>查看更多评价</text>
          <uni-icons type="right" size="16" color="#0066E9"></uni-icons>
        </view>
        
        <!-- 展示部分评价内容 -->
        <view class="preview-comments" v-if="showPreviewComments">
          <view class="preview-comments-title">最新评价</view>
          <view class="preview-comment-item" v-for="(comment, index) in previewComments" :key="index">
            <view class="preview-user-info">
              <view class="preview-user-avatar">
                <image :src="comment.avatar || defaultAvatar" mode="aspectFill" />
              </view>
              <view class="preview-user-detail">
                <view class="preview-user-name">{{comment.userName || '匿名用户'}}</view>
                <view class="preview-user-time">{{comment.time}}</view>
              </view>
              <view class="preview-user-rate">
                <t-star-rating 
                  :value="comment.rate" 
                  :readonly="true"
                  :size="16"
                />
              </view>
            </view>
            <view class="preview-comment-text">{{comment.text}}</view>
            <view class="preview-expand-btn" v-if="comment.hasDetails" @tap="navigateToDetail(comment.id)">
              <text>展开对话</text>
              <uni-icons type="right" size="14" color="#999999"></uni-icons>
            </view>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 添加悬浮评价按钮 -->
    <view class="floating-btn" @tap="goToAddEvaluation">
      <uni-icons type="plusempty" size="24" color="#FFFFFF"></uni-icons>
    </view>
    
    <!-- 评价详情弹出层 -->
    <view class="comment-dialog-overlay" v-if="dialogVisible" @tap="closeCommentDialog">
      <view class="comment-dialog" @tap.stop>
        <!-- 对话框标题 -->
        <view class="comment-dialog-header">
          <view class="comment-dialog-title">用户评价</view>
          <view class="comment-dialog-close" @tap="closeCommentDialog">
            <uni-icons type="close" size="24" color="#333333"></uni-icons>
          </view>
        </view>
        
        <!-- 对话内容区域，可滚动 -->
        <scroll-view class="comment-dialog-content" scroll-y>
          <!-- 对话列表 -->
          <view class="dialog-message-list">
            <view 
              class="dialog-message-item" 
              :class="[item.userType === 'official' ? 'official' : 'user']"
              v-for="(item, index) in commentDialogList" 
              :key="index"
            >
              <!-- 用户信息 -->
              <view class="dialog-user-info">
                <view class="dialog-user-avatar">
                  <image :src="item.avatar || defaultAvatar" mode="aspectFill" />
                </view>
                <view class="dialog-user-detail">
                  <view class="dialog-user-name">
                    {{item.userName}}
                    <text class="dialog-user-tag" v-if="item.userType === 'official'">平台官方人员</text>
                  </view>
                  <view class="dialog-message-time">{{item.time}}</view>
                </view>
              </view>
              
              <!-- 对话内容 -->
              <view class="dialog-message-content">{{item.content}}</view>
            </view>
          </view>
        </scroll-view>
        
        <!-- 底部回复框 -->
        <view class="comment-dialog-footer">
          <view class="comment-reply-btn" @tap="showReplyDialog">
            <uni-icons type="chatbubble" size="20" color="#0066E9"></uni-icons>
            <text>回复</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 回复弹出层 -->
    <view class="reply-dialog-overlay" v-if="replyDialogVisible" @tap="closeReplyDialog">
      <view class="reply-dialog" @tap.stop>
        <!-- 回复框标题 -->
        <view class="reply-dialog-header">
          <view class="reply-dialog-title">回复"{{currentComment ? currentComment.userName : ''}}"</view>
          <view class="reply-dialog-close" @tap="closeReplyDialog">
            <uni-icons type="close" size="20" color="#333333"></uni-icons>
          </view>
        </view>
        
        <!-- 回复内容输入区 -->
        <view class="reply-dialog-content">
          <textarea 
            class="reply-textarea" 
            placeholder="请输入回复内容" 
            v-model="replyContent"
            maxlength="200"
            auto-height
          ></textarea>
          <view class="reply-word-count">{{replyContent.length}}/200</view>
          
          <!-- 图片上传 -->
          <view class="reply-image-upload">
            <view class="upload-image-placeholder">
              <uni-icons type="image" size="32" color="#AAAAAA"></uni-icons>
            </view>
            <view class="upload-image-placeholder">
              <uni-icons type="image" size="32" color="#AAAAAA"></uni-icons>
            </view>
            <view class="upload-image-add">
              <uni-icons type="plusempty" size="32" color="#AAAAAA"></uni-icons>
              <text>添加图片</text>
            </view>
          </view>
        </view>
        
        <!-- 回复按钮 -->
        <view class="reply-dialog-footer">
          <button 
            class="reply-submit-btn" 
            :disabled="replyContent.trim() === '' || isReplySubmitting"
            :loading="isReplySubmitting"
            @tap="submitReply"
          >回复</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue';
import TStarRating from '@/components/common/t-star-rating.vue';
import TEvaluationTags from '@/components/common/t-evaluation-tags.vue';
import TEvaluationItems from '@/components/common/t-evaluation-items.vue';
import evaluationUtils from '@/pagesD/utils/evaluationUtils';

export default {
  components: {
    uniIcons,
    TStarRating,
    TEvaluationTags,
    TEvaluationItems
  },
  data() {
    return {
      // 评价类型：serviceArea(服务区), tollStation(收费站), chargingService(充电服务)
      evaluationType: 'serviceArea',
      // 评价ID
      evaluationId: '',
      // 业务ID
      businessId: '',
      // 评价数据
      evaluationData: {
        businessInfo: {
          name: '',
          detail: '',
          time: ''
        },
        rate: 0,
        text: '',
        images: [],
        tags: [],
        time: '',
        serviceAreaItems: [],
        tollStationItems: [],
        chargingItems: [],
        reply: null // 添加回复字段
      },
      // 默认头像
      defaultAvatar: '../../static/images/default-avatar.png',
      // 是否显示预览评论
      showPreviewComments: true,
      // 预览评论列表
      previewComments: [],
      
      // 新增：弹出层相关数据
      dialogVisible: false, // 评价详情弹出层是否可见
      replyDialogVisible: false, // 回复弹出层是否可见
      currentComment: null, // 当前查看的评价
      replyContent: '', // 回复内容
      commentDialogList: [], // 评价详情对话列表
      isReplySubmitting: false, // 回复是否正在提交
    };
  },
  computed: {
    // 根据评价类型返回页面标题
    pageTitle() {
      return evaluationUtils.getEvaluationTitle(this.evaluationType);
    },
    
    // 判断是否有评价数据
    hasEvaluation() {
      // 修改判断条件：只要有业务信息和评分不为0，就认为有评价数据
      return this.evaluationData && 
        this.evaluationData.businessInfo && 
        this.evaluationData.businessInfo.name && 
        this.evaluationData.rate > 0;
    }
  },
  onLoad(options) {
    // 设置页面标题（使用小程序自带头部title）
    uni.setNavigationBarTitle({
      title: this.pageTitle
    });
    
    // 从路由参数获取评价类型和ID
    if (options.type) {
      this.evaluationType = options.type;
      // 更新页面标题
      uni.setNavigationBarTitle({
        title: this.pageTitle
      });
    }
    
    if (options.businessId) {
      this.businessId = options.businessId;
      // 获取业务信息
      this.getBusinessInfo();
    }
    
    // 测试无评价状态
    if (options.noEvaluation === 'true') {
      // 只获取业务信息，不获取评价数据
      this.getBusinessInfo();
      return;
    }
    
    if (options.id) {
      this.evaluationId = options.id;
      // 获取评价详情
      this.getEvaluationDetail();
    } else if (options.evaluationData) {
      // 如果直接传递了评价数据
      try {
        this.evaluationData = JSON.parse(decodeURIComponent(options.evaluationData));
      } catch (error) {
        console.error('解析评价数据失败', error);
      }
    } else {
      // 如果没有评价数据，只获取业务信息
      this.getBusinessInfo();
      // 默认仍然加载一个模拟评价，避免显示暂无评价
      this.getEvaluationDetail();
    }
    
    // 获取预览评论
    this.getPreviewComments();
    
    // 检查是否需要显示弹出层
    if (options.showDialog === 'true' && options.commentId) {
      // 延迟执行，确保数据已加载
      setTimeout(() => {
        const commentId = options.commentId;
        const comment = this.previewComments.find(item => item.id === commentId);
        if (comment) {
          this.showCommentDialog(comment);
        }
      }, 800);
    }
  },
  methods: {
    // 获取满意度文本
    getSatisfactionText(rate) {
      return evaluationUtils.getSatisfactionText(rate);
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取业务信息
    getBusinessInfo() {
      // 这里应该调用API获取业务信息
      // 模拟获取业务信息
      setTimeout(() => {
        // 根据评价类型设置不同的业务信息
        switch (this.evaluationType) {
          case 'serviceArea':
            this.evaluationData.businessInfo = {
              name: '高岭服务区',
              detail: '南宁方向',
              time: '2023/05/20 14:30'
            };
            break;
          case 'tollStation':
            this.evaluationData.businessInfo = {
              name: '南宁收费站',
              detail: '出口',
              time: '2023/05/20 14:30'
            };
            break;
          case 'chargingService':
            this.evaluationData.businessInfo = {
              name: '南宁充电站',
              detail: '快充服务',
              time: '2023/05/20 14:30'
            };
            break;
          default:
            break;
        }
      }, 500);
    },
    
    // 获取评价详情
    getEvaluationDetail() {
      // 这里应该调用API获取评价详情
      // 模拟获取评价详情
      setTimeout(() => {
        // 根据评价类型设置不同的模拟数据
        switch (this.evaluationType) {
          case 'serviceArea':
            this.evaluationData = {
              businessInfo: {
                name: '高岭服务区',
                detail: '南宁方向',
                time: '2023/05/20 14:30'
              },
              rate: 5,
              text: '服务很好，环境整洁，体验很棒！',
              images: [],
              tags: ['环境整洁', '服务热情'],
              time: '2023/05/20 14:30',
              serviceAreaItems: [
                { name: '环境卫生', rate: 5 },
                { name: '服务态度', rate: 4 },
                { name: '设施完善度', rate: 5 },
                { name: '商品价格', rate: 3 },
                { name: '整体体验', rate: 4 }
              ],
              reply: {
                content: '感谢您对高岭服务区的评价与支持！我们将继续提升服务质量，为您提供更好的服务体验。欢迎您再次光临！',
                time: '2023/05/21 09:15'
              }
            };
            break;
          case 'tollStation':
            this.evaluationData = {
              businessInfo: {
                name: '南宁收费站',
                detail: '出口',
                time: '2023/05/20 14:30'
              },
              rate: 4,
              text: '收费速度快，服务态度好。',
              images: [],
              tags: ['通行顺畅', '服务规范'],
              time: '2023/05/20 14:30',
              tollStationItems: [
                { name: '通行速度', rate: 5 },
                { name: '服务态度', rate: 4 },
                { name: '收费准确性', rate: 5 },
                { name: '整体体验', rate: 4 }
              ],
              reply: {
                content: '感谢您的评价！我们将继续努力提升服务质量，为广大车主提供更加便捷的通行体验。',
                time: '2023/05/22 10:30'
              }
            };
            break;
          case 'chargingService':
            this.evaluationData = {
              businessInfo: {
                name: '南宁充电站',
                detail: '快充服务',
                time: '2023/05/20 14:30'
              },
              rate: 4,
              text: '充电速度快，设备状态良好。',
              images: [],
              tags: ['充电快速', '设备先进'],
              time: '2023/05/20 14:30',
              chargingItems: [
                { name: '充电速度', rate: 4 },
                { name: '设备状态', rate: 5 },
                { name: '操作便捷性', rate: 4 },
                { name: '服务态度', rate: 5 },
                { name: '价格合理性', rate: 3 }
              ]
            };
            break;
          default:
            break;
        }
      }, 500);
      
      // 实际API调用
      // this.$request.get(this.$interfaces.getEvaluationDetail, { 
      //   data: { id: this.evaluationId } 
      // }).then(res => {
      //   if (res.code === 200) {
      //     this.evaluationData = res.data;
      //   }
      // }).catch(err => {
      //   console.error('获取评价详情失败', err);
      //   uni.showToast({
      //     title: '获取评价详情失败',
      //     icon: 'none'
      //   });
      // });
    },
    
    // 获取预览评论
    getPreviewComments() {
      // 模拟获取预览评论数据
      setTimeout(() => {
        switch (this.evaluationType) {
          case 'serviceArea':
            this.previewComments = [
              {
                id: 'srv1',
                userName: '挑事的网上邻居',
                avatar: '',
                time: '2025/03/08 01:04',
                rate: 1,
                text: '这是一段恶评...',
                hasDetails: true
              },
              {
                id: 'srv5',
                userName: '网上邻居2',
                avatar: '',
                time: '2025/03/06 02:04',
                rate: 5,
                text: 'good!!!',
                hasDetails: true
              }
            ];
            break;
          case 'tollStation':
            this.previewComments = [
              {
                id: 'toll1',
                userName: '匆匆过客',
                avatar: '',
                time: '2025/03/07 18:30',
                rate: 5,
                text: '收费站通行非常顺畅，工作人员服务态度很好！',
                hasDetails: true
              }
            ];
            break;
          case 'chargingService':
            this.previewComments = [
              {
                id: 'charge1',
                userName: '新能源车主',
                avatar: '',
                time: '2025/03/05 15:20',
                rate: 4,
                text: '充电速度还可以，设备比较新，就是价格略贵。',
                hasDetails: true
              }
            ];
            break;
          default:
            this.previewComments = [];
        }
      }, 500);
    },
    
    // 预览图片
    previewImage(image) {
      uni.previewImage({
        urls: this.evaluationData.images,
        current: image
      });
    },
    
    // 跳转到评论列表页面
    goToCommentList() {
      uni.navigateTo({
        url: `/pagesD/evaluationPage/commentList?type=${this.evaluationType}&businessId=${this.businessId}`
      });
    },
    
    // 导航到评价详情页 - 弹出层形式
    navigateToDetail(commentId) {
      const comment = this.previewComments.find(item => item.id === commentId);
      if (!comment) return;
      
      this.showCommentDialog(comment);
    },
    
    // 显示评价详情弹出层
    showCommentDialog(comment) {
      // 设置当前评价
      this.currentComment = JSON.parse(JSON.stringify(comment));
      
      // 获取评价详情和对话历史
      this.getCommentDetail(comment.id);
      
      // 显示弹出层
      this.dialogVisible = true;
    },
    
    // 获取评价详情和对话历史
    getCommentDetail(commentId) {
      // 这里应该调用API获取评价详情和对话历史
      // 模拟获取数据
      setTimeout(() => {
        // 模拟对话数据
        this.commentDialogList = [
          {
            id: '1',
            userName: this.currentComment.userName,
            avatar: this.currentComment.avatar || '',
            userType: 'user',
            time: this.currentComment.time,
            content: this.currentComment.text
          }
        ];
        
        // 如果有回复，添加到对话列表
        if (this.currentComment.reply) {
          this.commentDialogList.push({
            id: '2',
            userName: '服务区服务人员一号',
            avatar: '',
            userType: 'official',
            time: this.currentComment.reply.time,
            content: this.currentComment.reply.content
          });
        }
        
        // 如果有多轮对话，这里可以添加模拟数据
        if (commentId === 'srv1') {
          this.commentDialogList.push({
            id: '3',
            userName: '服务区服务人员一号',
            avatar: '',
            userType: 'official',
            time: '2025/03/08 01:10',
            content: '这是一段安慰的话...'
          });
          
          this.commentDialogList.push({
            id: '4',
            userName: this.currentComment.userName,
            avatar: this.currentComment.avatar || '',
            userType: 'user',
            time: '2025/03/08 01:12',
            content: '这又是一段恶评...'
          });
          
          this.commentDialogList.push({
            id: '5',
            userName: '服务区服务人员一号',
            avatar: '',
            userType: 'official',
            time: '2025/03/08 01:14',
            content: '这又是一段安慰的话...'
          });
        }
      }, 300);
    },
    
    // 关闭评价详情弹出层
    closeCommentDialog() {
      this.dialogVisible = false;
      this.currentComment = null;
      this.commentDialogList = [];
    },
    
    // 显示回复弹出层
    showReplyDialog() {
      this.replyDialogVisible = true;
    },
    
    // 关闭回复弹出层
    closeReplyDialog() {
      this.replyDialogVisible = false;
      this.replyContent = '';
    },
    
    // 提交回复
    submitReply() {
      if (!this.replyContent.trim()) {
        uni.showToast({
          title: '请输入回复内容',
          icon: 'none'
        });
        return;
      }
      
      this.isReplySubmitting = true;
      
      // 这里应该调用API提交回复
      // 模拟提交
      setTimeout(() => {
        // 添加回复到对话列表
        this.commentDialogList.push({
          id: new Date().getTime().toString(),
          userName: '平台官方人员',
          avatar: '',
          userType: 'official',
          time: this.formatDateTime(new Date()),
          content: this.replyContent
        });
        
        // 重置回复内容
        this.replyContent = '';
        
        // 关闭回复弹出层
        this.closeReplyDialog();
        
        this.isReplySubmitting = false;
        
        uni.showToast({
          title: '回复成功',
          icon: 'success'
        });
      }, 800);
    },
    
    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hour = date.getHours().toString().padStart(2, '0');
      const minute = date.getMinutes().toString().padStart(2, '0');
      
      return `${year}/${month}/${day} ${hour}:${minute}`;
    },
    
    // 跳转到添加评价页面
    goToAddEvaluation() {
      try {
        // 在执行页面跳转前，确保处理可能存在的autofill问题
        if (document && document.activeElement && document.activeElement.blur) {
          document.activeElement.blur();
        }
        
        // 处理可能的自动填充下拉框问题
        setTimeout(() => {
          uni.navigateTo({
            url: `/pagesD/evaluationPage/index?type=${this.evaluationType}&businessId=${this.businessId}&businessName=${encodeURIComponent(this.evaluationData.businessInfo.name)}`
          });
        }, 100);
      } catch (e) {
        console.error('页面跳转失败', e);
        // 降级处理，直接进行页面跳转
        uni.navigateTo({
          url: `/pagesD/evaluationPage/index?type=${this.evaluationType}&businessId=${this.businessId}&businessName=${encodeURIComponent(this.evaluationData.businessInfo.name)}`
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.evaluation-show-page {
  min-height: 100vh;
  background-color: #F5F5F5;
  position: relative;
}

.evaluation-content {
  padding: 20rpx;
  box-sizing: border-box;
}

.business-info {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.business-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
}

.business-detail {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.business-time {
  font-size: 24rpx;
  color: #999999;
}

.no-evaluation {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.no-evaluation-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 40rpx;
}

.evaluation-box {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.satisfaction-text {
  font-size: 32rpx;
  color: #333333;
  text-align: center;
  margin: 20rpx 0;
  font-weight: 500;
}

.evaluation-text {
  background-color: #F8F8F8;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 24rpx 0;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx 0;
  gap: 16rpx;
}

.uploaded-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.image-preview {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.evaluation-time {
  font-size: 24rpx;
  color: #AAAAAA;
  text-align: right;
  margin-top: 20rpx;
}

/* 添加回复内容的样式 */
.reply-container {
  margin-top: 30rpx;
  background-color: #F8F8F8;
  border-radius: 12rpx;
  padding: 24rpx;
  position: relative;
}

.reply-container:before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: 30rpx;
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-bottom: 10rpx solid #F8F8F8;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.reply-title {
  font-size: 26rpx;
  color: #0066E9;
  font-weight: 600;
}

.reply-time {
  font-size: 24rpx;
  color: #999999;
}

.reply-content {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.6;
}

/* 查看更多评论按钮 */
.more-comments-btn {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 30rpx;
  margin: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #0066E9;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 预览评论样式 */
.preview-comments {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx 0 60rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.preview-comments-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #EEEEEE;
  padding-bottom: 16rpx;
}

.preview-comment-item {
  border-bottom: 1rpx solid #F5F5F5;
  padding: 20rpx 0;
}

.preview-comment-item:last-child {
  border-bottom: none;
}

.preview-user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.preview-user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15rpx;
  
  image {
    width: 100%;
    height: 100%;
  }
}

.preview-user-detail {
  flex: 1;
}

.preview-user-name {
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 4rpx;
}

.preview-user-time {
  font-size: 22rpx;
  color: #999999;
}

.preview-user-rate {
  margin-left: auto;
}

.preview-comment-text {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.5;
  margin-left: 75rpx;
  margin-bottom: 10rpx;
}

.preview-expand-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #999999;
}

/* 添加悬浮按钮样式 */
.floating-btn {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #0066E9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 102, 233, 0.3);
  z-index: 100;
  transition: all 0.3s;
}

.floating-btn:active {
  transform: scale(0.95);
  background-color: #0055cc;
}

/* 评价详情弹出层样式 */
.comment-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comment-dialog {
  width: 90%;
  height: 80%;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}

.comment-dialog-header {
  height: 90rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #EEEEEE;
}

.comment-dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.comment-dialog-close {
  padding: 10rpx;
}

.comment-dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 30rpx;
}

.dialog-message-list {
  display: flex;
  flex-direction: column;
}

.dialog-message-item {
  margin-bottom: 40rpx;
}

.dialog-message-item.official {
  align-self: flex-start;
}

.dialog-message-item.user {
  align-self: flex-end;
}

.dialog-user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.dialog-user-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15rpx;
  
  image {
    width: 100%;
    height: 100%;
  }
}

.dialog-user-detail {
  display: flex;
  flex-direction: column;
}

.dialog-user-name {
  font-size: 26rpx;
  color: #333333;
  display: flex;
  align-items: center;
}

.dialog-user-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #0066E9;
  background-color: rgba(0, 102, 233, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
}

.dialog-message-time {
  font-size: 22rpx;
  color: #999999;
  margin-top: 4rpx;
}

.dialog-message-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  padding: 20rpx;
  background-color: #F8F8F8;
  border-radius: 12rpx;
  margin-left: 85rpx;
  position: relative;
}

.dialog-message-item.official .dialog-message-content {
  background-color: #F8F8F8;
}

.dialog-message-item.user .dialog-message-content {
  background-color: #E5F2FF;
}

.comment-dialog-footer {
  height: 90rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-top: 1rpx solid #EEEEEE;
}

.comment-reply-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  padding: 0 20rpx;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  color: #0066E9;
  font-size: 26rpx;
}

.comment-reply-btn text {
  margin-left: 8rpx;
}

/* 回复弹出层样式 */
.reply-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reply-dialog {
  width: 90%;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}

.reply-dialog-header {
  height: 80rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #EEEEEE;
}

.reply-dialog-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.reply-dialog-close {
  padding: 10rpx;
}

.reply-dialog-content {
  padding: 30rpx;
}

.reply-textarea {
  width: 100%;
  min-height: 160rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333333;
  background-color: #F5F5F5;
  border-radius: 12rpx;
  line-height: 1.6;
}

.reply-word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}

.reply-image-upload {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.upload-image-placeholder,
.upload-image-add {
  width: 140rpx;
  height: 140rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-image-add text {
  font-size: 22rpx;
  color: #999999;
  margin-top: 8rpx;
}

.reply-dialog-footer {
  padding: 20rpx 30rpx 30rpx;
}

.reply-submit-btn {
  width: 100%;
  height: 80rpx;
  background-color: #0066E9;
  color: #FFFFFF;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reply-submit-btn[disabled] {
  opacity: 0.6;
  background-color: #CCCCCC;
}
</style>
