/**
 * 评价工具类
 */

// 满意度级别定义
export const satisfactionLevels = [
  { min: 1, max: 1, text: '非常不满意' },
  { min: 2, max: 2, text: '不满意' },
  { min: 3, max: 3, text: '一般' },
  { min: 4, max: 4, text: '满意' },
  { min: 5, max: 5, text: '非常满意' }
];

// 各类型评价的标签
export const evaluationTags = {
  serviceArea: {
    positive: ['环境整洁', '服务热情', '设施完善', '价格合理', '商品丰富'],
    negative: ['环境脏乱', '服务态度差', '设施陈旧', '价格偏高', '商品种类少']
  },
  tollStation: {
    positive: ['通行顺畅', '服务规范', '收费准确', '态度友好', '环境整洁'],
    negative: ['通行缓慢', '服务不规范', '收费错误', '态度恶劣', '环境脏乱']
  },
  chargingService: {
    positive: ['充电快速', '设备先进', '操作简便', '价格合理', '服务周到'],
    negative: ['充电缓慢', '设备老旧', '操作复杂', '价格偏高', '服务差']
  }
};

// 各类型评价的项目定义
export const evaluationItems = {
  serviceArea: [
    { name: '环境卫生', rate: 0 },
    { name: '服务态度', rate: 0 },
    { name: '设施完善度', rate: 0 },
    { name: '商品价格', rate: 0 },
    { name: '整体体验', rate: 0 }
  ],
  tollStation: [
    { name: '通行速度', rate: 0 },
    { name: '服务态度', rate: 0 },
    { name: '收费准确性', rate: 0 },
    { name: '整体体验', rate: 0 }
  ],
  chargingService: [
    { name: '充电速度', rate: 0 },
    { name: '设备状态', rate: 0 },
    { name: '操作便捷性', rate: 0 },
    { name: '服务态度', rate: 0 },
    { name: '价格合理性', rate: 0 }
  ]
};

// 评价类型的标题映射
export const evaluationTitles = {
  serviceArea: '服务区评价',
  tollStation: '收费站评价',
  chargingService: '充电服务评价'
};

/**
 * 获取满意度文本
 * @param {Number} rate 评分
 * @returns {String} 满意度文本
 */
export function getSatisfactionText(rate) {
  const level = satisfactionLevels.find(
    level => rate >= level.min && rate <= level.max
  );
  
  return level ? level.text : '非常满意';
}

/**
 * 根据评价类型和评分获取评价标签
 * @param {String} type 评价类型
 * @param {Number} rate 评分
 * @returns {Array} 标签列表
 */
export function getEvaluationTagsByType(type, rate) {
  // 根据评分返回正面或负面标签
  const isPositive = rate >= 3;
  return isPositive
    ? (evaluationTags[type]?.positive || [])
    : (evaluationTags[type]?.negative || []);
}

/**
 * 获取评价项目的初始值
 * @param {String} type 评价类型
 * @returns {Array} 评价项目列表
 */
export function getInitialItemsByType(type) {
  return JSON.parse(JSON.stringify(evaluationItems[type] || []));
}

/**
 * 获取评价类型的标题
 * @param {String} type 评价类型
 * @returns {String} 标题
 */
export function getEvaluationTitle(type) {
  return evaluationTitles[type] || '用户评价';
}

/**
 * 判断是否有评价数据
 * @param {Object} data 评价数据
 * @returns {Boolean} 是否有评价数据
 */
export function hasEvaluationData(data) {
  return data && 
    data.rate > 0 && 
    (data.text || (data.tags && data.tags.length > 0));
}

/**
 * 格式化当前时间为评价时间格式
 * @returns {String} 格式化后的时间字符串
 */
export function formatEvaluationTime() {
  const now = new Date();
  return `${now.getFullYear()}/${
    (now.getMonth() + 1).toString().padStart(2, '0')
  }/${
    now.getDate().toString().padStart(2, '0')
  } ${
    now.getHours().toString().padStart(2, '0')
  }:${
    now.getMinutes().toString().padStart(2, '0')
  }`;
}

export default {
  satisfactionLevels,
  evaluationTags,
  evaluationItems,
  evaluationTitles,
  getSatisfactionText,
  getEvaluationTagsByType,
  getInitialItemsByType,
  getEvaluationTitle,
  hasEvaluationData,
  formatEvaluationTime
}; 