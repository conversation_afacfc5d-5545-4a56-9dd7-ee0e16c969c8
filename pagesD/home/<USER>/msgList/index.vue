<template>
  <view class="msg-list">
    <view class="scroll-box">

        <msgItem v-for="(item, idx) in msgList" :key="idx" :itemInfo="item" />
        <!-- <load-more :loadStatus="noticeLoadStatus" /> -->
    </view>

    <tLoading :isShow="isLoading" />
    <u-empty mode="data" v-if="msgList.length == 0" color="#999"></u-empty>
  </view>
</template>

<script>
import { getLoginUserInfo, getEtcAccountInfo } from "@/common/storageUtil.js";
import msgItem from "./msg-item.vue";
import tLoading from "@/components/common/t-loading.vue";
// import loadMore from "@/pagesB/components/load-more/index.vue";
import mapAPI from "@/common/api/map.js";
export default {
  components: {
    msgItem,
    tLoading
    // loadMore
  },
  data() {
    return {
      msgList: [],
      isLoading: false,
      hasNewMessage: 0
    };
  },
  methods: {
    async getList() {
      let params = {
        custMastId: getEtcAccountInfo().custMastId,
        // custMastId: '2860784'
      };
      if (this.hasNewMessage == 1) {
        params.isReadType = '1' // 0: 未读, 1: 已读
      }
      let res = await this.$request.post(mapAPI.messageList, {
        data: params
      });
      if (res.code == 200 && res.data) {
        console.log(res.data);
        this.msgList = res.data;
      }
    }
  },
  onLoad(options) {
    this.hasNewMessage = options.hasNewMessage || 0
    this.getList();
  }
};
</script>

<style lang="scss" scoped>
.msg-list {
  padding: 20rpx;
}
</style>