<template>
  <view class="chat-container">
    <!-- 聊天消息区域 -->
    <scroll-view
      class="chat-messages"
      scroll-y="true"
      :scroll-top="scrollTop"
      scroll-with-animation="true"
      ref="messagesScroll"
      @scroll="handleScroll"
      :enhanced="true"
      :show-scrollbar="false"
    >
      <view class="messages-container" id="messagesContainer">
        <!-- 使用AgentMessageItem组件显示消息 -->
        <agent-message-item
          v-for="(message, index) in messages"
          :key="message.id"
          :message="message"
          :index="index"
          :isTyping="isMessageTyping(message)"
          :typingContent="getMessageDisplayContent(message)"
          :cards="message.hasCards ? getMessageCards(message.id) : []"
          :shouldShowTime="shouldShowTime(message, index)"
          @cardClick="handleCardClick"
          @showMoreCards="showMoreCards"
          @contentRendered="scrollToBottom"
          @contentUpdated="scrollToBottomIfNearEnd"
        />

        <!-- AI typing indicator -->
        <view v-if="isAiTyping" class="message-wrapper">
          <view class="message-item ai-message">
            <view class="message-content">
              <view class="typing-indicator">
                <view class="typing-dots">
                  <view class="dot"></view>
                  <view class="dot"></view>
                  <view class="dot"></view>
                </view>
                <text class="typing-text">AI正在思考中...</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 使用AgentInputPanel组件 -->
    <agent-input-panel
      v-model="inputText"
      :isRecording="isRecording"
      @sendMessage="handleSend"
      @startRecord="handleVoiceStart"
      @stopRecord="handleVoiceEnd"
      @clearChat="handleClearChat"
      @keyboardHeightChange="handleKeyboardHeightChange"
    />
  </view>
</template>

<script>
// 引入自定义组件
import AgentMessageItem from './components/AgentMessageItem.vue'
import AgentInputPanel from './components/AgentInputPanel.vue'

// 引入性能优化管理器
import DataLayerManager from './dataManager.js'
import ObjectPoolManager from './objectPool.js'

// 引入语音相关插件
const plugin = requirePlugin("WechatSI")
const innerAudioContext = uni.createInnerAudioContext()

// 模拟AI回复数据
const aiResponses = [
  {
    trigger: ['你好', '您好', 'hi', 'hello'],
    response: {
      type: 'text',
      content: '您好！我是小通AI智能出行助手。您可以向我提问如"推荐广西三天两夜的行程规划"，我将根据您的需求为您定制专属行程。'
    }
  },
  {
    trigger: ['推荐', '行程', '旅游', '广西', '柳州', '景点'],
    response: {
      type: 'text',
      content: '好的，根据您现在所在的城市，向您推荐好玩的景点',
      cards: [
        {
          id: 1,
          title: '青秀山风景区',
          rating: '5A',
          description: '南宁打卡景点榜第1名',
          imageUrl: ''
        },
        {
          id: 2,
          title: '青秀山风景区',
          rating: '5A',
          description: '南宁打卡景点榜第1名',
          imageUrl: ''
        },
        {
          id: 3,
          title: '青秀山风景区',
          rating: '5A',
          description: '南宁打卡景点榜第1名',
          imageUrl: ''
        }
      ]
    }
  },
  {
    trigger: ['谢谢', '感谢', '不错', '好的'],
    response: {
      type: 'text',
      content: '很高兴能为您提供帮助！如果您还有其他旅游相关问题，随时可以询问我。祝您旅途愉快！🎉'
    }
  }
]

export default {
  name: 'ChatAgent',
  components: {
    AgentMessageItem,
    AgentInputPanel
  },
  data() {
    return {
      // === 核心业务数据层（保持响应式） ===
      inputText: '',
      messages: [],
      isRecording: false,
      isAiTyping: false,
      maxMessages: 20,

      // === 渲染层数据（选择性响应式） ===
      scrollTop: 0,
      keyboardHeight: 0,
      isNearBottom: true,
      lastScrollTop: 0,
      isAutoScrolling: true,

      // === 基础状态数据（保持响应式） ===
      isPlaying: false, // 是否正在播放语音
      currentPlayingId: null, // 当前正在播放的消息ID

      // === 打字相关数据已移至非响应式管理 ===
      // typingStates: {}, // 已移除，使用 DataLayerManager
      // messageCards: {}, // 已移除，使用 DataLayerManager 缓存
      // typingConfig: {}, // 已移除，使用 DataLayerManager 配置
      // performanceMonitor: {}, // 已移除，使用 DataLayerManager 监控
      // cachedDomInfo: {}, // 已移除，使用 DataLayerManager 缓存

      // === 兼容性保留（逐步迁移） ===
      typingSpeed: 50 // 向后兼容，将逐步移除
    }
  },
  computed: {
    // 计算每个消息的显示内容 - 优化为使用 DataLayerManager
    getMessageDisplayContent() {
      return (message) => {
        if (DataLayerManager.getTempData('_isDestroyed')) return message.content
        
        const typingManager = DataLayerManager.getTempData('typingManager')
        if (typingManager.isTyping(message.id)) {
          return typingManager.getDisplayContent(message.id)
        }
        
        return message.content
      }
    },
    
    // 计算消息是否在打字状态 - 优化为使用 DataLayerManager
    isMessageTyping() {
      return (message) => {
        if (DataLayerManager.getTempData('_isDestroyed')) return false
        
        const typingManager = DataLayerManager.getTempData('typingManager')
        return typingManager.isTyping(message.id)
      }
    }
  },
  created() {
    // 初始化数据分层管理器
    DataLayerManager.initialize(this)
    
    // 检测iOS平台
    this.isIOS = uni.getSystemInfoSync().platform === 'ios'
    
    // 使用节流函数包装滚动处理
    this.handleScroll = this.throttle(this._handleScroll, 200)
    
    // 监听音频播放完成事件
    innerAudioContext.onEnded(() => {
      if (!DataLayerManager.getTempData('_isDestroyed')) {
        this.isPlaying = false
        this.currentPlayingId = null
      }
    })
    
    innerAudioContext.onError((res) => {
      console.log('音频播放错误:', res)
      if (!DataLayerManager.getTempData('_isDestroyed')) {
        this.isPlaying = false
        this.currentPlayingId = null
      }
    })
    
    // 所有平台的错误处理（不限制iOS）
    if (typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('unhandledrejection', this.handleUnhandledRejection)
    }
  },
  mounted() {
    console.log('页面挂载完成')
    if (!DataLayerManager.getTempData('_isDestroyed')) {
      this.initWelcomeMessage()
    }
  },
  methods: {
    // 安全的定时器创建
    _safeSetTimeout(callback, delay) {
      if (this._isDestroyed) {
        return null
      }
      
      // 如果_timers不存在，初始化它
      if (!this._timers) {
        this._timers = {
          intervals: new Set(),
          timeouts: new Set()
        }
      }
      
      const id = setTimeout(() => {
        if (this._timers && this._timers.timeouts) {
          this._timers.timeouts.delete(id)
        }
        if (!this._isDestroyed) {
          callback()
        }
      }, delay)
      
      if (this._timers && this._timers.timeouts) {
        this._timers.timeouts.add(id)
      }
      return id
    },
    
    _safeSetInterval(callback, delay) {
      if (this._isDestroyed) {
        return null
      }
      
      // 如果_timers不存在，初始化它
      if (!this._timers) {
        this._timers = {
          intervals: new Set(),
          timeouts: new Set()
        }
      }
      
      const id = setInterval(() => {
        if (this._isDestroyed) {
          clearInterval(id)
          if (this._timers && this._timers.intervals) {
            this._timers.intervals.delete(id)
          }
          return
        }
        callback()
      }, delay)
      
      if (this._timers && this._timers.intervals) {
        this._timers.intervals.add(id)
      }
      return id
    },
    
    // 限制消息数量（优化为使用DataLayerManager）
    limitMessages() {
      DataLayerManager.limitMessages()
    },
    
    // 初始化欢迎消息
    initWelcomeMessage() {
      const welcomeMessage = {
        id: this.generateMessageId(),
        type: 'ai',
        content: '你好，这里是桂小通AI智慧出行......有问题可以随时向我提问',
        timestamp: Date.now(),
        isHtml: false
      }
      this.messages.push(welcomeMessage)

      // 逐字显示欢迎消息
      this.typeMessage(welcomeMessage)
    },
    
    // 高性能打字效果（优化为非响应式）
    typeMessage(message) {
      if (DataLayerManager.getTempData('_isDestroyed')) {
        return
      }
      
      // 清除之前的打字效果
      this.clearTypingEffect()
      
      const fullContent = message.content
      if (!fullContent) return
      
      // 使用 DataLayerManager 启动打字效果
      DataLayerManager.startTypingEffect(
        message,
        // 更新回调
        () => {
          // 强制更新视图（使用$forceUpdate减少响应式开销）
          this.$forceUpdate()
        },
        // 完成回调
        () => {
          this.scrollToBottom()
        }
      )
    },
    
    // 安全的requestAnimationFrame包装器
    _safeRequestAnimationFrame(callback) {
      if (typeof requestAnimationFrame !== 'undefined') {
        return requestAnimationFrame(callback)
      } else {
        // uni-app环境降级使用setTimeout
        return this._safeSetTimeout(callback, 16) // 约60fps
      }
    },
    
    // 安全的cancelAnimationFrame包装器
    _safeCancelAnimationFrame(id) {
      if (typeof cancelAnimationFrame !== 'undefined') {
        cancelAnimationFrame(id)
      } else {
        // 降级处理
        if (this._timers && this._timers.timeouts) {
          clearTimeout(id)
          this._timers.timeouts.delete(id)
        }
      }
    },
    
    // 安全的动画帧集合操作
    _safeAddAnimationFrame(id) {
      if (!this._animationFrames) {
        this._animationFrames = new Set()
      }
      this._animationFrames.add(id)
    },
    
    _safeDeleteAnimationFrame(id) {
      if (this._animationFrames) {
        this._animationFrames.delete(id)
      }
    },
    
    // 启动打字动画循环
    startTypingAnimation(messageId, fullContent) {
      if (this._isDestroyed || !this.typingStates[messageId]) {
        return
      }
      
      const state = this.typingStates[messageId]
      const currentTime = Date.now() // 使用Date.now()替代performance.now()以确保兼容性
      
      // 检查是否该更新字符
      if (currentTime - state.lastUpdateTime >= this.typingConfig.speed) {
        // 分块处理字符，提高性能
        const endIndex = Math.min(
          state.currentIndex + this.typingConfig.chunkSize,
          fullContent.length
        )
        
        // 更新显示内容
        state.displayContent = fullContent.substring(0, endIndex)
        state.currentIndex = endIndex
        state.lastUpdateTime = currentTime
        
        // 每处理一定数量字符后滚动
        if (endIndex % 20 === 0) {
          this.scrollToBottomIfNearEnd()
        }
        
        // 检查内存使用
        this.checkMemoryUsage()
      }
      
      // 检查是否完成
      if (state.currentIndex >= fullContent.length) {
        this.clearTypingEffect()
        this.scrollToBottom()
        return
      }
      
      // 继续下一帧
      const animationId = this._safeRequestAnimationFrame(() => {
        this.startTypingAnimation(messageId, fullContent)
      })
      
      state.animationId = animationId
      this._safeAddAnimationFrame(animationId)
    },
    
    // 增强的内存使用监控
    checkMemoryUsage() {
      const now = Date.now() // 使用Date.now()确保兼容性
      
      // 限制检查频率
      if (now - this.performanceMonitor.lastMemoryCheck < this.performanceMonitor.memoryCheckInterval) {
        return
      }
      
      this.performanceMonitor.lastMemoryCheck = now
      
      if (typeof performance !== 'undefined' && performance.memory) {
        const memory = performance.memory
        const memoryInfo = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          timestamp: now
        }
        
        // 记录内存使用历史
        this.performanceMonitor.memoryUsageHistory.push(memoryInfo)
        
        // 只保留最近10次记录
        if (this.performanceMonitor.memoryUsageHistory.length > 10) {
          this.performanceMonitor.memoryUsageHistory.shift()
        }
        
        // 检查内存使用
        const memoryUsagePercent = (memoryInfo.used / memoryInfo.limit) * 100
        
        if (memoryInfo.used > this.typingConfig.maxMemoryUsage) {
          console.warn(`内存使用超限: ${(memoryInfo.used / 1024 / 1024).toFixed(2)}MB (${memoryUsagePercent.toFixed(1)}%)`)
          this.optimizeTypingPerformance()
        }
        
        // 检查内存增长趋势
        if (this.performanceMonitor.memoryUsageHistory.length >= 3) {
          const recent = this.performanceMonitor.memoryUsageHistory.slice(-3)
          const isIncreasing = recent.every((curr, index) => {
            return index === 0 || curr.used > recent[index - 1].used
          })
          
          if (isIncreasing) {
            console.warn('检测到内存持续增长，启用性能优化')
            this.optimizeTypingPerformance()
          }
        }
      }
    },
    
    // 打字性能优化
    optimizeTypingPerformance() {
      // 动态调整配置
      this.typingConfig.chunkSize = Math.min(this.typingConfig.chunkSize * 1.5, 15)
      this.typingConfig.speed = Math.max(this.typingConfig.speed * 0.8, 30)
    },
    
    // 性能统计报告
    getPerformanceReport() {
      return {
        typingCount: this.performanceMonitor.typingCount,
        memoryHistory: this.performanceMonitor.memoryUsageHistory,
        currentConfig: this.typingConfig,
        activeAnimations: this._animationFrames ? this._animationFrames.size : 0
      }
    },
    
    // 高性能富文本打字效果（优化为非响应式）
    typeRichMessage(message) {
      if (DataLayerManager.getTempData('_isDestroyed')) return
      
      // 清除之前的打字效果
      this.clearTypingEffect()
      
      const fullContent = message.content
      if (!fullContent) return
      
      // 使用 DataLayerManager 启动富文本打字效果
      DataLayerManager.startTypingEffect(
        message,
        // 更新回调
        () => {
          // 强制更新视图（使用$forceUpdate减少响应式开销）
          this.$forceUpdate()
        },
        // 完成回调
        () => {
          this.scrollToBottom()
        }
      )
    },
    
    // 解析HTML令牌
    parseHtmlTokens(htmlContent) {
      // 使用正则表达式分离HTML标签和文本内容
      const tokens = htmlContent.match(/<[^>]+>|[^<]+/g) || []
      return tokens.map(token => ({
        content: token,
        isTag: token.startsWith('<'),
        processed: false
      }))
    },
    
    // 启动富文本打字动画循环
    startRichTypingAnimation(messageId) {
      if (this._isDestroyed || !this.typingStates[messageId]) {
        return
      }
      
      const state = this.typingStates[messageId]
      const currentTime = Date.now() // 使用Date.now()确保兼容性
      
      // 检查是否该更新
      if (currentTime - state.lastUpdateTime >= this.typingConfig.speed) {
        this.processRichTokens(state)
        state.lastUpdateTime = currentTime
      }
      
      // 检查是否完成
      if (state.currentTokenIndex >= state.tokens.length) {
        this.clearTypingEffect()
        this.scrollToBottom()
        return
      }
      
      // 继续下一帧
      const animationId = this._safeRequestAnimationFrame(() => {
        this.startRichTypingAnimation(messageId)
      })
      
      state.animationId = animationId
      this._safeAddAnimationFrame(animationId)
    },
    
    // 处理富文本令牌
    processRichTokens(state) {
      let processed = 0
      
      while (processed < this.typingConfig.chunkSize && state.currentTokenIndex < state.tokens.length) {
        const currentToken = state.tokens[state.currentTokenIndex]
        
        if (currentToken.isTag) {
          // HTML标签直接添加
          if (!currentToken.processed) {
            state.displayContent += currentToken.content
            currentToken.processed = true
            state.currentTokenIndex++
          }
        } else {
          // 文本内容逐字添加
          const textContent = currentToken.content
          if (state.currentCharIndex < textContent.length) {
            const endIndex = Math.min(
              state.currentCharIndex + this.typingConfig.chunkSize - processed,
              textContent.length
            )
            
            const chunk = textContent.substring(state.currentCharIndex, endIndex)
            state.displayContent += chunk
            state.currentCharIndex = endIndex
            processed += chunk.length
            
            if (state.currentCharIndex >= textContent.length) {
              currentToken.processed = true
              state.currentTokenIndex++
              state.currentCharIndex = 0
            }
          }
        }
        
        processed++
      }
      
      // 定期滚动
      if (state.currentTokenIndex % 5 === 0) {
        this.scrollToBottomIfNearEnd()
      }
    },
    
    // 清除打字效果（优化为非响应式管理）
    clearTypingEffect() {
      const typingManager = DataLayerManager.getTempData('typingManager')
      if (typingManager) {
        // 使用打字管理器的清理方法
        typingManager.clearAll()
      }
    },
    
    // 处理发送消息（优化为使用DataLayerManager）
    handleSend(text) {
      if (!text.trim()) return
      
      // 添加用户消息
      const userMessage = {
        id: this.generateMessageId(),
        type: 'user',
        content: text.trim(),
        timestamp: Date.now(),
        isHtml: false
      }
      
      DataLayerManager.addMessage(userMessage)
      this.isNearBottom = true
      this.inputText = '' // 清空输入框
      
      this.$nextTick(() => {
        this.scrollToBottom()
      })
      
      // 模拟AI回复
      this.simulateAiResponse(text.trim())
    },
    
    // 模拟AI回复（优化为使用DataLayerManager）
    simulateAiResponse(userInput) {
      if (DataLayerManager.getTempData('_isDestroyed')) {
        return
      }
      
      this.isAiTyping = true
      this.scrollToBottom()
      
      // 模拟思考时间
      setTimeout(() => {
        if (DataLayerManager.getTempData('_isDestroyed')) {
          return
        }
        
        const response = this.getAiResponse(userInput)  
        this.isAiTyping = false
        
        const messageId = this.generateMessageId()
        const aiMessage = {
          id: messageId,
          type: 'ai',
          content: response.content,
          timestamp: Date.now(),
          isHtml: response.type === 'rich',
          hasCards: !!(response.cards && response.cards.length > 0)
        }
        
        // 将卡片数据使用DataLayerManager存储
        if (response.cards && response.cards.length > 0) {
          DataLayerManager.setMessageCards(messageId, response.cards)
        }
        
        DataLayerManager.addMessage(aiMessage)
        
        // 根据消息类型选择不同的打字效果方法
        if (aiMessage.isHtml) {
          this.typeRichMessage(aiMessage)
        } else {
          this.typeMessage(aiMessage)
        }
        
        this.$nextTick(() => {
          if (!DataLayerManager.getTempData('_isDestroyed')) {
            this.scrollToBottom()
          }
        })
      }, 1500)
    },
    
    // 获取AI回复内容
    getAiResponse(userInput) {
      const input = userInput.toLowerCase()
      
      for (let responseData of aiResponses) {
        if (responseData.trigger.some(trigger => input.includes(trigger))) {
          return responseData.response
        }
      }
      
      // 默认回复
      return {
        type: 'text',
        content: '抱歉，我还在学习中，请尝试询问关于广西旅游的问题，比如"推荐广西三天两夜的行程规划"。'
      }
    },
    
    // 优化的滚动到底部方法（使用DataLayerManager缓存）
    scrollToBottom() {
      this.$nextTick(() => {
        const cache = DataLayerManager.getDomCache()
        
        // 如果缓存有效，直接使用缓存数据
        if (cache && cache.containerHeight > 0) {
          const scrollPosition = cache.containerHeight - cache.scrollViewHeight + 100
          if (scrollPosition > 0) {
            this.scrollTop = scrollPosition
            this.lastScrollTop = this.scrollTop
            this.isAutoScrolling = true
          }
          return
        }
        
        // 需要重新查询DOM
        const query = uni.createSelectorQuery().in(this)
        query.select('#messagesContainer').boundingClientRect(data => {
          if (data) {
            query.select('.chat-messages').boundingClientRect(scrollData => {
              if (scrollData) {
                // 更新DataLayerManager缓存
                DataLayerManager.updateDomCache({
                  containerHeight: data.height,
                  scrollViewHeight: scrollData.height
                })
                
                // 计算滚动位置
                const scrollPosition = data.height - scrollData.height + 100
                if (scrollPosition > 0) {
                  this.scrollTop = scrollPosition
                  this.lastScrollTop = this.scrollTop
                  this.isAutoScrolling = true
                }
              }
            }).exec()
          }
        }).exec()
      })
    },
    
    // 只在接近底部时才自动滚动
    scrollToBottomIfNearEnd() {
      if (this.isNearBottom) {
        this.scrollToBottom()
      }
    },
    
    // 优化的滚动事件处理
    _handleScroll(e) {
      // 用户手动滚动时
      if (!this.isAutoScrolling) {
        const scrollTop = e.detail.scrollTop
        this.lastScrollTop = scrollTop
        
        // 使用缓存的DOM信息计算是否接近底部，避免频繁查询
        const cache = this.cachedDomInfo
        if (cache.containerHeight > 0 && cache.scrollViewHeight > 0) {
          const distanceToBottom = cache.containerHeight - (scrollTop + cache.scrollViewHeight)
          this.isNearBottom = distanceToBottom < 100
        } else {
          // 缓存无效时，降低查询频率
          if (!this._scrollQueryPending && !this._isDestroyed) {
            this._scrollQueryPending = true
            this._safeSetTimeout(() => {
              if (this._isDestroyed) {
                this._scrollQueryPending = false
                return
              }
              
              const query = uni.createSelectorQuery().in(this)
              query.select('#messagesContainer').boundingClientRect(data => {
                if (this._isDestroyed) {
                  this._scrollQueryPending = false
                  return
                }
                
                if (data) {
                  query.select('.chat-messages').boundingClientRect(scrollView => {
                    if (this._isDestroyed) {
                      this._scrollQueryPending = false
                      return
                    }
                    
                    if (scrollView) {
                      const distanceToBottom = data.height - (scrollTop + scrollView.height)
                      this.isNearBottom = distanceToBottom < 100
                      
                      // 更新缓存
                      cache.containerHeight = data.height
                      cache.scrollViewHeight = scrollView.height
                      cache.lastCacheTime = Date.now()
                    }
                    this._scrollQueryPending = false
                  }).exec()
                }
              }).exec()
            }, 100) // 延迟100ms执行，避免频繁查询
          }
        }
      }
      // 重置自动滚动标记
      this.isAutoScrolling = false
    },
    
    // 处理语音录制开始
    handleVoiceStart() {
      this.isRecording = true
      uni.vibrateShort()
      uni.showToast({
        title: '长按录音',
        icon: 'none'
      })
    },
    
    // 处理语音录制结束
    handleVoiceEnd() {
      this.isRecording = false
      uni.showToast({
        title: '录音结束',
        icon: 'none'
      })
      
      // 这里模拟语音转文字结果
      setTimeout(() => {
        const recognizedText = "推荐广西三天两夜的行程规划";
        this.handleSend(recognizedText);
      }, 1000);
    },
    
    // 清空聊天记录
    handleClearChat() {
      uni.showModal({
        title: '提示',
        content: '确定要清空所有聊天记录吗？',
        success: (res) => {
          if (res.confirm) {
            this.messages = []
            this.initWelcomeMessage()
          }
        }
      })
    },
    
    // 处理卡片点击
    handleCardClick(card) {
      uni.showToast({
        title: `点击了${card.title}`,
        icon: 'none'
      })
    },
    
    // 生成消息ID
    generateMessageId() {
      return Date.now().toString() + Math.random().toString(36).substring(2, 11)
    },
    
    // 播放语音
    playVoice(content) {
      if (this.isPlaying) {
        // 如果正在播放，先停止当前播放
        innerAudioContext.stop()
        this.isPlaying = false
        this.currentPlayingId = null
        return
      }
      
      // 开始新的语音播放
      this.isPlaying = true
      
      // 利用WechatSI插件进行文本转语音
      plugin.textToSpeech({
        lang: "zh_CN",
        tts: true,
        content: content,
        success: (res) => {
          this.playAudio(res.filename)
        },
        fail: () => {
          this.isPlaying = false

          uni.showToast({
            title: '语音播放失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 播放音频文件
    playAudio(src) {
      if (!src) {
        this.isPlaying = false
        return
      }
      
      innerAudioContext.autoplay = true
      innerAudioContext.src = src
      
      innerAudioContext.play()
      
      // 显示正在播放提示
      uni.showToast({
        title: '正在播放语音',
        icon: 'none',
        duration: 2000
      })
    },
    
    // 处理折叠卡片事件
    handleToggleFold(isFolded) {
      // 折叠后滚动到适当位置
      if (isFolded) {
        // 向上滚动一定距离
        const currentScrollTop = this.scrollTop
        this.scrollTop = currentScrollTop - 300
      } else {
        // 展开时滚动到底部
        this.scrollToBottom()
      }
    },
    
    // 处理键盘高度变化
    handleKeyboardHeightChange(height) {
      this.keyboardHeight = height
      // 键盘弹起时总是滚动到底部
      if (height > 0) {
        setTimeout(() => {
          this.scrollToBottom()
        }, 300) // 延迟执行确保键盘完全弹起后再滚动
      }
    },
    
    // 获取消息的卡片数据（优化为使用DataLayerManager）
    getMessageCards(messageId) {
      return DataLayerManager.getMessageCards(messageId)
    },

    // 展示更多卡片
    showMoreCards() {
      uni.showToast({
        title: '展示更多功能',
        icon: 'none'
      })
    },

    // 处理键盘高度变化
    handleKeyboardHeightChange(height) {
      this.keyboardHeight = height
      if (height > 0) {
        // 键盘弹起时滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 判断是否显示时间
    shouldShowTime(message, index) {
      if (index === 0) return true

      const prevMessage = this.messages[index - 1]
      const timeDiff = message.timestamp - prevMessage.timestamp

      // 超过5分钟显示时间
      return timeDiff > 5 * 60 * 1000
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    },
    
    // 节流函数（内存安全版）
    throttle(fn, delay) {
      let timer = null
      const self = this
      return function(...args) {
        if (self._isDestroyed) return
        if (!timer) {
          fn.apply(self, args)
          timer = setTimeout(() => {
            timer = null
          }, delay)
        }
      }
    },
    
    // 增强的错误处理机制
    handleUnhandledRejection(event) {
      // 视图相关错误处理
      if (event.reason && event.reason.errMsg) {
        const errMsg = event.reason.errMsg
        
        // 处理各种视图错误
        if (errMsg.includes('removeTextView') || 
            errMsg.includes('removeBaseView') ||
            errMsg.includes('no root view') ||
            errMsg.includes('view not found')) {
          event.preventDefault()
          
          // 尝试重新初始化视图（延迟执行避免竞态条件）
          this._safeSetTimeout(() => {
            if (!this._isDestroyed) {
              this.reinitializeViewSafely()
            }
          }, 100)
          return
        }
        
        // 处理其他类型的错误
        if (errMsg.includes('webview') || errMsg.includes('component')) {
          event.preventDefault()
          return
        }
      }
      
      // 其他未知错误，记录但不阻止
    },
    
    // 安全的视图重新初始化
    reinitializeViewSafely() {
      try {
        // 检查关键DOM元素是否存在
        this.$nextTick(() => {
          if (this.$refs.messagesScroll) {
          } else {
            this._safeSetTimeout(() => {
              if (!this._isDestroyed) {
                this.reinitializeViewSafely()
              }
            }, 500)
          }
        })
      } catch (error) {
        console.error('视图重新初始化异常:', error)
      }
    }
  },
  
    // 组件销毁时清理资源（优化为使用DataLayerManager）
  beforeDestroy() {
    // 标记组件销毁状态
    DataLayerManager.setTempData('_isDestroyed', true)
    
    // 停止音频播放
    if (innerAudioContext) {
      try {
        innerAudioContext.stop()
      } catch (e) {
        console.log('音频停止失败:', e)
      }
    }
    
    // 销毁数据管理器（包含所有资源清理）
    DataLayerManager.destroy()
    
    // 销毁对象池管理器
    ObjectPoolManager.destroy()
    
    // 清理错误监听器（所有平台）
    if (typeof window !== 'undefined' && window.removeEventListener) {
      window.removeEventListener('unhandledrejection', this.handleUnhandledRejection)
    }
    
    console.log('组件资源清理完成（优化版）')
  }
}
</script>

<style lang="scss" scoped>
.chat-container {
  height: 100vh;
  background-color: #f7f7f7;
  overflow: hidden;
  position: relative;
}

.chat-messages {
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  height: calc(100vh - 280rpx);
  transition: padding-bottom 0.3s ease;
}

.messages-container {
  padding: 24rpx 24rpx;
  position: relative;
}

.message-wrapper {
  margin-bottom: 32rpx;
}

// AI thinking animation
.typing-indicator {
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  border-bottom-left-radius: 8rpx;
  margin-top: 8rpx;

  .typing-dots {
    display: flex;
    margin-right: 16rpx;

    .dot {
      width: 8rpx;
      height: 8rpx;
      border-radius: 50%;
      background-color: #007AFF;
      margin-right: 6rpx;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .typing-text {
    font-size: 28rpx;
    color: #666;
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
