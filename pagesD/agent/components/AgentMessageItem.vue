<template>
  <view class="message-item" :class="message.type + '-message'">
    <!-- 消息内容 -->
    <view class="message-content">
      <!-- 文本消息 -->
      <view v-if="!message.isHtml" class="text-bubble" :class="message.type + '-bubble'">
        <text class="message-text">{{ message.type === 'ai' ? (isTyping ? typingContent : message.content) : message.content }}</text>
        <view v-if="message.type === 'ai' && isTyping" class="typing-cursor"></view>

        <!-- AI消息语音播放按钮 -->
        <view v-if="message.type === 'ai' && !isTyping" class="voice-play-btn" @click="playVoice(message.content)">
          <uni-icons type="sound" size="20" color="#8e65de"/>
        </view>
      </view>

      <!-- 富文本消息 -->
      <view v-else class="text-bubble ai-bubble">
        <rich-text :nodes="message.type === 'ai' ? (isTyping ? typingContent : message.content) : message.content" class="rich-content"></rich-text>
        <view v-if="isTyping" class="typing-cursor"></view>

        <!-- AI富文本消息语音播放按钮 -->
        <view v-if="message.type === 'ai' && !isTyping" class="voice-play-btn" @click="playVoice(message.content.replace(/<[^>]*>/g, ''))">
          <uni-icons type="sound" size="20" color="#8e65de"/>
        </view>
      </view>
    </view>

    <!-- 景点卡片独立布局 -->
    <view v-if="cards && cards.length > 0 && !isTyping" class="cards-container" :class="{'cards-folded': isFolded}">
      <view class="cards-header" @click="toggleFold">
        <uni-icons type="location-filled" size="18" color="#8e65de" />
        <text class="cards-title">推荐景区</text>
        <view class="fold-icon">
          <uni-icons :type="isFolded ? 'bottom' : 'top'" size="14" color="#999" />
        </view>
      </view>

      <view class="cards-list" v-if="!isFolded">
        <view
          v-for="card in cards"
          :key="card.id"
          class="card-item"
          @click="handleCardClick(card)"
        >
          <view class="card-image">
            <image v-if="card.imageUrl" :src="card.imageUrl" mode="aspectFill" class="card-img" />
            <view v-else class="card-img-placeholder"></view>
            <view v-if="card.rating" class="card-rating">{{ card.rating }}</view>
          </view>
          <view class="card-content">
            <view class="card-title">{{ card.title }}</view>
            <view v-if="card.description" class="card-desc">{{ card.description }}</view>
          </view>
          <view class="card-arrow">
            <uni-icons type="right" size="16" color="#ccc" />
          </view>
        </view>
      </view>
    </view>

    <!-- 时间显示 -->
    <view v-if="shouldShowTime" class="time-display">
      <text class="time-text">{{ formatTime(message.timestamp) }}</text>
    </view>
  </view>
</template>

<script>
// 引入uni-icons组件
import uniIcons from '@/components/uni-icons/uni-icons.vue'

export default {
  name: 'AgentMessageItem',
  components: {
    uniIcons
  },
  props: {
    message: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      default: 0
    },
    isTyping: {
      type: Boolean,
      default: false
    },
    typingContent: {
      type: String,
      default: ''
    },
    cards: {
      type: Array,
      default: () => []
    },
    shouldShowTime: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEntering: false,
      isFolded: false
    }
  },
  mounted() {
    this.triggerEnterAnimation()
  },
  watch: {
    // 监听打字内容变化，降低触发频率以优化性能
    typingContent() {
      if (this.isTyping) {
        // 使用节流，每200ms最多触发一次
        if (!this._contentUpdateTimer) {
          this._contentUpdateTimer = setTimeout(() => {
            this.$emit('contentUpdated')
            this._contentUpdateTimer = null
          }, 200)
        }
      }
    }
  },
  methods: {
    // 触发渐入动画
    triggerEnterAnimation() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.isEntering = true
          // 通知父组件滚动到底部
          this.$emit('contentRendered')
        }, this.index * 100) // 每个消息延迟100ms
      })
    },
    
    // 处理卡片点击
    handleCardClick(card) {
      this.$emit('cardClick', card)
    },
    
    // 展示更多卡片
    showMoreCards() {
      this.$emit('showMoreCards')
    },

    // 切换折叠状态
    toggleFold() {
      this.isFolded = !this.isFolded
      this.$emit('toggleFold', this.isFolded)
    },

    // 播放语音
    playVoice(content) {
      this.$emit('playVoice', content)
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.message-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 32rpx;

  &.ai-message {
    align-items: flex-start;

    .message-content {
      align-items: flex-start;
    }
  }

  &.user-message {
    align-items: flex-end;

    .message-content {
      align-items: flex-end;
    }
  }
}

.message-content {
  max-width: 90%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-out;
}

.text-bubble {
  padding: 24rpx 32rpx;
  border-radius: 24rpx;
  word-wrap: break-word;
  position: relative;
  font-size: 32rpx;
  line-height: 1.4;

  &.ai-bubble {
    background-color: #fff;
    border: 1rpx solid #e0e0e0;
    padding-right: 64rpx;  // 为语音按钮留出空间
    border-bottom-left-radius: 8rpx;

    .message-text {
      color: #333;
    }
  }

  &.user-bubble {
    background-color: #8e65de;
    border-bottom-right-radius: 8rpx;

    .message-text {
      color: #fff;
    }
  }
}

.message-text {
  font-size: 32rpx;
  line-height: 1.4;
}

.typing-cursor {
  display: inline-block;
  width: 4rpx;
  height: 32rpx;
  background-color: #8e65de;
  vertical-align: middle;
  margin-left: 4rpx;
  animation: blink 1s infinite;
}

/* 高性能打字动画样式 */
.text-typing-animation {
  overflow: hidden;
  white-space: nowrap;
  border-right: 2rpx solid #8e65de;
}

.text-typing-complete {
  border-right: none;
}

/* 减少重绘的优化样式 */
.message-text {
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: auto;
}

.typing-content {
  backface-visibility: hidden;
  transform: translateZ(0);
}

.rich-content {
  font-size: 32rpx;
  line-height: 1.4;
  color: #333;
}

.voice-play-btn {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f0f0f0;
  z-index: 2;

  &:active {
    background-color: #e0e0e0;
  }
}

.time-display {
  text-align: center;
  margin: 24rpx 0;

  .time-text {
    font-size: 24rpx;
    color: #999;
  }
}

// 景点卡片样式
.cards-container {
  margin-top: 24rpx;
  width: 100%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;

  &.cards-folded {
    border-radius: 16rpx;
  }
}

.cards-header {
  padding: 24rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;

  .cards-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-left: 12rpx;
  }

  .fold-icon {
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%);
  }
}

.cards-list {
  padding: 8rpx 0;
}

.card-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  transition: background-color 0.2s ease;
  position: relative;

  &:active {
    background-color: #f8f9fa;
  }

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 24rpx;
    right: 24rpx;
    bottom: 0;
    height: 1rpx;
    background-color: #f5f5f5;
  }
}

.card-image {
  width: 140rpx;
  height: 110rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 24rpx;
  position: relative;
  flex-shrink: 0;

  .card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .card-img-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
  }

  .card-rating {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    background-color: rgba(255, 255, 255, 0.9);
    color: #8e65de;
    font-size: 22rpx;
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
    font-weight: 600;
  }
}

.card-content {
  flex: 1;
  overflow: hidden;

  .card-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .card-desc {
    font-size: 24rpx;
    color: #999;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.card-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 16rpx;

  .arrow-icon {
    font-size: 36rpx;
    color: #ccc;
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
</style>
