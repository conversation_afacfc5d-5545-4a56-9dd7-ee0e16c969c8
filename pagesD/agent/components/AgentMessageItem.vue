<template>
  <view class="agent-message-wrapper">
    <view class="message-item" :class="message.type + '-message'">
      <!-- AI消息 -->
      <view v-if="message.type === 'ai'" class="message-item ai-message">
        <view class="avatar-wrapper">
          <view class="ai-avatar">
            <view class="robot-icon">
              <view class="robot-head"></view>
              <view class="robot-eyes">
                <view class="robot-eye"></view>
                <view class="robot-eye"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="message-content">
          <view class="message-bubble ai-bubble">
            <text v-if="!message.isHtml" class="message-text">
              {{ message.type === 'ai' ? (isTyping ? typingContent : message.content) : message.content }}
            </text>
            <rich-text v-else :nodes="message.type === 'ai' ? (isTyping ? typingContent : message.content) : message.content" class="rich-content"></rich-text>
            <view v-if="message.type === 'ai' && isTyping" class="typing-cursor"></view>
          </view>

          <!-- 景点卡片 -->
          <view v-if="cards && cards.length > 0 && !isTyping" class="cards-container">
            <view
              v-for="card in cards"
              :key="card.id"
              class="card-item"
              @click="handleCardClick(card)"
            >
              <view class="card-image">
                <image v-if="card.imageUrl" :src="card.imageUrl" mode="aspectFill" class="card-img" />
                <view v-else class="card-img-placeholder"></view>
                <view v-if="card.rating" class="card-rating">{{ card.rating }}</view>
              </view>
              <view class="card-content">
                <view class="card-title">{{ card.title }}</view>
                <view v-if="card.description" class="card-desc">{{ card.description }}</view>
              </view>
              <view class="card-arrow">
                <uni-icons type="right" size="16" color="#ccc" />
              </view>
            </view>

            <!-- 展示更多按钮 -->
            <view v-if="cards.length > 3" class="show-more-btn" @click="showMoreCards">
              <text class="show-more-text">展示更多</text>
              <uni-icons type="bottom" size="14" color="#999" />
            </view>
          </view>
        </view>
      </view>

      <!-- 用户消息 -->
      <view v-else class="message-item user-message">
        <view class="message-content">
          <view class="message-bubble user-bubble">
            <text class="message-text">{{ message.content }}</text>
          </view>
        </view>
        <view class="avatar-wrapper">
          <view class="user-avatar">
            <view class="user-icon">
              <view class="user-head"></view>
              <view class="user-body"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 时间显示 -->
      <view v-if="shouldShowTime" class="time-display">
        <text class="time-text">{{ formatTime(message.timestamp) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
// 引入uni-icons组件
import uniIcons from '@/components/uni-icons/uni-icons.vue'

export default {
  name: 'AgentMessageItem',
  components: {
    uniIcons
  },
  props: {
    message: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      default: 0
    },
    isTyping: {
      type: Boolean,
      default: false
    },
    typingContent: {
      type: String,
      default: ''
    },
    cards: {
      type: Array,
      default: () => []
    },
    shouldShowTime: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEntering: false
    }
  },
  mounted() {
    this.triggerEnterAnimation()
  },
  watch: {
    // 监听打字内容变化，降低触发频率以优化性能
    typingContent() {
      if (this.isTyping) {
        // 使用节流，每200ms最多触发一次
        if (!this._contentUpdateTimer) {
          this._contentUpdateTimer = setTimeout(() => {
            this.$emit('contentUpdated')
            this._contentUpdateTimer = null
          }, 200)
        }
      }
    }
  },
  methods: {
    // 触发渐入动画
    triggerEnterAnimation() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.isEntering = true
          // 通知父组件滚动到底部
          this.$emit('contentRendered')
        }, this.index * 100) // 每个消息延迟100ms
      })
    },
    
    // 处理卡片点击
    handleCardClick(card) {
      this.$emit('cardClick', card)
    },
    
    // 展示更多卡片
    showMoreCards() {
      this.$emit('showMoreCards')
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.agent-message-wrapper {
  position: relative;
  margin-bottom: 32rpx;
}



.message-item {
  display: flex;
  align-items: flex-start;

  &.ai-message {
    flex-direction: row;
    justify-content: flex-start;
  }
  &.user-message {
    justify-content: flex-end;
  }
}

.ai-avatar, .user-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-avatar {
  margin-right: 20rpx;
  background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
}

.user-avatar {
  margin-left: 20rpx;
  background: linear-gradient(135deg, #FF8C42 0%, #E67E22 100%);
}

// AI机器人图标样式
.robot-icon {
  position: relative;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.robot-head {
  width: 20rpx;
  height: 16rpx;
  background-color: #fff;
  border-radius: 4rpx 4rpx 2rpx 2rpx;
  position: relative;
  margin-bottom: 2rpx;
}

.robot-eyes {
  position: absolute;
  top: 4rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 4rpx;
}

.robot-eye {
  width: 3rpx;
  height: 3rpx;
  background-color: #007AFF;
  border-radius: 50%;
}

// 用户头像图标样式
.user-icon {
  position: relative;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.user-head {
  width: 12rpx;
  height: 12rpx;
  background-color: #fff;
  border-radius: 50%;
  margin-bottom: 2rpx;
}

.user-body {
  width: 20rpx;
  height: 14rpx;
  background-color: #fff;
  border-radius: 10rpx 10rpx 0 0;
}

.message-content {
  flex: 1;
  max-width: calc(100% - 120rpx);
}

.message-bubble {
  padding: 24rpx 32rpx;
  border-radius: 24rpx;
  word-wrap: break-word;
  position: relative;

  &.ai-bubble {
    background-color: #fff;
    border-bottom-left-radius: 8rpx;
    margin-top: 8rpx;
  }

  &.user-bubble {
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
    border-bottom-left-radius: 24rpx;
    border-bottom-right-radius: 8rpx;
    margin-top: 8rpx;

    .message-text {
      color: #fff;
    }
  }
}

.message-text {
  font-size: 32rpx;
  line-height: 1.5;
  color: #333;
}

.rich-content {
  font-size: 32rpx;
  line-height: 1.5;
  color: #333;
}

.time-display {
  text-align: center;
  margin: 24rpx 0;

  .time-text {
    font-size: 24rpx;
    color: #999;
  }
}

// 景点卡片样式
.cards-container {
  margin-top: 16rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f9fa;
  }
}

.card-image {
  width: 120rpx;
  height: 90rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 24rpx;
  position: relative;

  .card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .card-img-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
  }

  .card-rating {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    background-color: rgba(255, 255, 255, 0.9);
    color: #FF8C00;
    font-size: 20rpx;
    padding: 4rpx 8rpx;
    border-radius: 4rpx;
    font-weight: 600;
  }
}

.card-content {
  flex: 1;
  overflow: hidden;

  .card-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .card-desc {
    font-size: 24rpx;
    color: #999;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.card-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.show-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-top: 1rpx solid #f5f5f5;
  gap: 8rpx;

  .show-more-text {
    font-size: 28rpx;
    color: #999;
  }

  &:active {
    background-color: #f8f9fa;
  }
}

.typing-cursor {
  display: inline-block;
  width: 4rpx;
  height: 32rpx;
  background-color: #007AFF;
  vertical-align: middle;
  margin-left: 4rpx;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
</style>
