{
	"easycom": {
		"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [{
			"path": "pages/home/<USER>/p-home",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white",
				"enablePullDownRefresh": true
			}
		},
		// {
		// 	"path": "pages/login/accountLogin",
		// 	"style": {
		// 		"navigationBarTitleText": "账号登录",
		// 		"mp-weixin": {
		// 			"usingComponents": {
		// 				"t-captcha": "plugin://captcha/t-captcha"
		// 			}

		// 		}
		// 	}
		// },

		// {
		// 	"path": "pages/login/p-login",
		// 	"style": {
		// 		"navigationBarTitleText": "微信登录"
		// 	}
		// },
		{
			"path": "pages/webview/getOpenIdwebview",
			"style": {
				"navigationBarTitleText": "公众号授权登录"
			}
		},
		{
			"path": "pages/uni-webview/uni-webview",
			"style": {
				"navigationBarTitleText": "桂小通(广西捷通)"
			}
		},
		{
			"path": "pages/uni-webview/h5-webview",
			"style": {
				"navigationBarTitleText": "桂小通(广西捷通)用户协议"
			}
		},
		// {
		// 	"path": "pages/register/p-register",
		// 	"style": {
		// 		"navigationBarTitleText": "用户注册",
		// 		"mp-weixin": {
		// 			"usingComponents": {
		// 				"t-captcha": "plugin://captcha/t-captcha"
		// 			}

		// 		}
		// 	}
		// },
		{
			"path": "pages/personal/account",
			"style": {
				"navigationBarTitleText": "我的ETC账户"
			}
		},
		{
			"path": "pages/bind/wechatBind",
			"style": {
				"navigationBarTitleText": "用户绑定"
			}
		}
		// #ifdef H5
		,
		{
			"path": "pages/bind/bind",
			"style": {
				"navigationBarTitleText": "用户绑定"
			}
		},
		{
			"path": "pages/wxauthorize/wxauthorize",
			"style": {
				"navigationBarTitleText": "微信公众号授权"
			}
		}
		// #endif
		,
		{
			"path": "pages/transfers/transfers",
			"style": {
				"navigationBarTitleText": "桂小通(广西捷通)"
			}
		},
		{
			"path": "pages/home/<USER>/highService",
			"style": {
				"navigationBarTitleText": "高速服务"
			}
		}

	],
	"subPackages": [{
		"root": "pagesA",
		"pages": [{
				"path": "newBusiness/personal/personal",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/installation/installation",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/productSelect/productSelect",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},

			{
				"path": "newBusiness/vehicle/vehicle",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/signature/signature",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/signature/signTutorial",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/signature/ccsSign",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "代扣签约"
				}
			},
			{
				"path": "newBusiness/signature/ccsSign/index",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "免密代扣签约"
				}
			},
			{
			    "path": "newBusiness/signature/ccsSign/attorney",
			    "style": {
			        "navigationBarTitleText": "委托扣款授权书"
			    }
			},
			{
				"path": "newBusiness/signature/agreement",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "代扣签约"
				}
			},
			{
				"path": "newBusiness/userAgreement/agreementList",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/order/order",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/order/orderDetail",
				"style": {
					"navigationBarTitleText": "订单详情"
				}

			},
			{
				"path": "newBusiness/order/orderProgress/orderProgressDetail",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "办理进度"
				}
			},
			{
				"path": "newBusiness/order/orderAddress/orderAddressDetail",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "取货信息"
				}
			},
			{
				"path": "newBusiness/order/orderWl/orderWlDetail",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "物流信息"
				}
			},
			{
				"path": "newBusiness/order/orderInfo/orderInfoDetail",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "申办资料详情"
				}
			},
			{
				"path": "newBusiness/order/orderProduct/orderProductDetail",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "产品信息详情"
				}
			},
			{
				"path": "newBusiness/order/orderSuccess",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/issue/issue",
				"style": {
					"navigationBarTitleText": "设备激活"
				}
			},
			{
				"path": "newBusiness/issue/changeIssue",
				"style": {
					"navigationBarTitleText": "更换补办激活"
				}

			},
			{
				"path": "newBusiness/issue/issue-upload",
				"style": {
					"navigationBarTitleText": "设备激活"
				}
			},
			{
				"path": "newBusiness/issue/issue-install",
				"style": {
					"navigationBarTitleText": "设备激活"
				}
			},
			{
				"path": "newBusiness/issue/issue-confirm",
				"style": {
					"navigationBarTitleText": "设备激活"
				}
			},
			{
				"path": "newBusiness/afterSale/select-type",
				"style": {
					"navigationBarTitleText": "售后选择"
				}
			},
			{
				"path": "newBusiness/afterSale/apply",
				"style": {
					"navigationBarTitleText": "售后申请"
				}
			},
			{
				"path": "newBusiness/afterSale/apply-list",
				"style": {
					"navigationBarTitleText": "售后申请记录"
				}
			},
			{
				"path": "newBusiness/afterSale/apply-detail",
				"style": {
					"navigationBarTitleText": "售后详情"
				}
			},
			{
				"path": "newBusiness/douyin/entry/entry",
				"style": {
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/douyin/entry/mallEntry",
				"style": {
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/douyin/carInfo/carInfo",
				"style": {
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/douyin/personal/personal",
				"style": {
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/douyin/vehicle/vehicle",
				"style": {
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/douyin/order/orderDetail",
				"style": {
					"navigationBarTitleText": "订单详情"
				}
			
			},
			{
				"path": "newBusiness/douyin/order/orderProgress/orderProgressDetail",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "办理进度"
				}
			},
			{
				"path": "newBusiness/douyin/order/orderInfo/orderInfoDetail",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "申办资料详情"
				}
			},
			{
				"path": "newBusiness/douyin/order/orderProduct/orderProductDetail",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "产品信息详情"
				}
			},
			{
				"path": "newBusiness/douyin/order/orderSuccess",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "newBusiness/douyin/userAgreement/agreementList",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "ETC办理"
				}
			},
			{
				"path": "allBusiness/allBusiness",
				"style": {
					"navigationBarTitleText": "桂小通(广西捷通)"
				}
			},
			{
				"path": "serviceOrder/index",
				"style": {
					"navigationBarTitleText": "桂小通(广西捷通)"
				}

			},
			{
				"path": "serviceOrder/afterSale/index",
				"style": {
					"navigationBarTitleText": "服务订单"
				}
			
			},
			{
				"path": "serviceOrder/other/orderTransfer",
				"style": {
					"navigationBarTitleText": "广西捷通ETC服务"
				}
			},
			{
				"path": "serviceOrder/other/orderSuccess",
				"style": {
					"navigationBarTitleText": "解除车牌占用申请"
				}
			},
			{
				"path": "newBusiness/issue/rewiteConfirm",
				"style": {
					"navigationBarTitleText": "卡签有效期续期"
				}
			},
			{
				"path": "newBusiness/issue/rewriteTimeIssue",
				"style": {
					"navigationBarTitleText": "卡签有效期续期"
				}
			}
		]
	}, {
		"root": "pagesB",
		"pages": [{
				"path": "personal/car-info/car-etcinfo",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/car-info/p-car-etcinfo",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/my-car/my-car-info",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/my-car/s-my-car-info",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/my-car/s-my-car",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/my-car/p-my-car",
				"style": {
					"navigationBarTitleText": "我的车辆"
				}
			},
			{
				"path": "personal/aboutus/s-aboutus",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/aboutus/p-aboutus",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/aboutus/s-feedback",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/aboutus/p-feedback",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/my-order/my-afterorder",
				"style": {
					"navigationBarTitleText": "我的ETC账户"
				}
			},
			{
				"path": "personal/more/s-myinfo",
				"style": {
					"navigationBarTitleText": "个人资料"
				}
			},
			{
				"path": "personal/more/p-myinfo",
				"style": {
					"navigationBarTitleText": "个人资料"
				}
			},
			{
				"path": "rechargeBusiness/recharge/p-recharge",
				"style": {
					"navigationBarTitleText": "卡账充值",
					"mp-weixin": {
						"usingComponents": {
							"t-captcha": "plugin://captcha/t-captcha"
						}
					}
				}
			},
			{
				"path": "rechargeBusiness/rechargeOther/rechargeOther",
				"style": {
					"navigationBarTitleText": "充值其他车辆"
				}
			},
			{
				"path": "rechargeBusiness/rechargeList/p-rechargeList",
				"style": {
					"navigationBarTitleText": "充值记录"
				}
			},
			{
				"path": "cardBusiness/payInfo/p-payInfo",
				"style": {
					"navigationBarTitleText": "通行记录"
				}
			},
			{
				"path": "cardBusiness/blackList/blackInfo",
				"style": {
					"navigationBarTitleText": "卡片状态查询"
				}
			},
			{
				"path": "loadBusiness/loadByCard",
				"style": {
					"navigationBarTitleText": "蓝牙圈存"
				}
			},
			{
				"path": "loadBusiness/loadByNFC",
				"style": {
					"navigationBarTitleText": "NFC圈存"
				}
			},
			{
				"path": "loadBusiness/loadType",
				"style": {
					"navigationBarTitleText": "充值/圈存方式选择"
				}
			},
			{
				"path": "loadBusiness/loadBusiness",
				"style": {
					"navigationBarTitleText": "圈存"
				}
			},
			{
				"path": "loadBusiness/recharge/recharge",
				"style": {
					"navigationBarTitleText": "储值卡充值",
					"mp-weixin": {
						"usingComponents": {
							"t-captcha": "plugin://captcha/t-captcha"
						}

					}
				}
			},
			{
				"path": "loadBusiness/recharge/rechargeRecord",
				"style": {
					"navigationBarTitleText": "储值卡充值记录"
				}
			},
			{
				"path": "loadBusiness/load/toLoadByCard",
				"style": {
					"navigationBarTitleText": "读卡充值/圈存"
				}
			},
			{
				"path": "loadBusiness/load/toLoadByNFC",
				"style": {
					"navigationBarTitleText": "NFC充值/圈存"
				}
			}, {
				"path": "loadBusiness/load/loadRecord",
				"style": {
					"navigationBarTitleText": "圈存记录"
				}
			},
			{
				"path": "containerRefund/car-record/record-list",
				"style": {
					"navigationBarTitleText": "集装箱通行记录"
				}
			},
			{
				"path": "containerRefund/car-record/record-detail",
				"style": {
					"navigationBarTitleText": "通行记录详情"
				}
			},
			{
				"path": "containerRefund/car-apply/refund-apply",
				"style": {
					"navigationBarTitleText": "集装箱退费申请"
				}
			},
			{
				"path": "containerRefund/car-apply/apply-list",
				"style": {
					"navigationBarTitleText": "申请记录"
				}
			},
			{
				"path": "containerRefund/car-apply/apply-detail",
				"style": {
					"navigationBarTitleText": "申请详情"
				}
			},
			{
				"path": "containerRefund/car-apply/apply-progress",
				"style": {
					"navigationBarTitleText": "进度查询"
				}
			},
			{
				"path": "ecssBusiness/index",
				"style": {
					"navigationBarTitleText": "账户迁移"
				}
			},
			{
				"path": "accountBusiness/otherBindCheck/otherBindCheck",
				"style": {
					"navigationBarTitleText": "ETC账户绑定",
					"mp-weixin": {
						"usingComponents": {
							"t-captcha": "plugin://captcha/t-captcha"
						}
					}
				}
			},
			{
				"path": "accountBusiness/otherBind/otherBind",
				"style": {
					"navigationBarTitleText": "ETC账户绑定"
				}
			},
			{
				"path": "vehicleBusiness/vehicleList",
				"style": {
					"navigationBarTitleText": "我的车辆"
				}
			},
			{
				"path": "vehicleBusiness/vehicleListCost",
				"style": {
					"navigationBarTitleText": "消费记录查询"
				}
			},
			{
				"path": "vehicleBusiness/noVehicleList",
				"style": {
					"navigationBarTitleText": "我的车辆"
				}
			},
			{
				"path": "accountBusiness/accountList/accountList",
				"style": {
					"navigationBarTitleText": "我的ETC账户",
					"mp-weixin": {
						"usingComponents": {
							"t-captcha": "plugin://captcha/t-captcha"
						}
					}
				}
			},
			{
				"path": "rechargeBusiness/rechargeOtherList/rechargeOtherList",
				"style": {
					"navigationBarTitleText": "代充记录"
				}
			},
			{
				"path": "personal/cardCharge/cardCharge",
				"style": {
					"navigationBarTitleText": "卡账充值"
				}
			},
			{
				"path": "accountBusiness/publicAccount/accountDetail",
				"style": {
					"navigationBarTitleText": "账户详情"
				}
			},
			{
				"path": "deviceBusiness/entry/entry",
				"style": {
					"navigationBarTitleText": "选择设备",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "deviceBusiness/updateImg/updateImg",
				"style": {
					"navigationBarTitleText": "资料补传",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "deviceBusiness/updateDevice/updateDevice",
				"style": {
					"navigationBarTitleText": "信息修正",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "deviceBusiness/updateSuccess/updateSuccess",
				"style": {
					"navigationBarTitleText": "更新成功",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "rechargeBusiness/rechargeAccount/index",
				"style": {
					"navigationBarTitleText": "账户充值",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black",
					"mp-weixin": {
						"usingComponents": {
							"t-captcha": "plugin://captcha/t-captcha"
						}
					}
				}
			},
			{
				"path": "rechargeBusiness/selectVehicle/index",
				"style": {
					"navigationBarTitleText": "充值",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "rechargeBusiness/rechargeAccountList/index",
				"style": {
					"navigationBarTitleText": "充值记录",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "rechargeBusiness/rechargeType/index",
				"style": {
					"navigationBarTitleText": "充值圈存",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "vehicleBusiness/vehicleDetail",
				"style": {
					"navigationBarTitleText": "车辆详情"
				}
			},
			{
				"path": "cardBusiness/blackList/vehicleState",
				"style": {
					"navigationBarTitleText": "车辆状态查询"
				}
			},
			{
				"path": "disputeRefund/index",
				"style": {
					"navigationBarTitleText": "退费申请",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "disputeRefund/addAccount",
				"style": {
					"navigationBarTitleText": "退款银行账户",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "disputeRefund/accountList",
				"style": {
					"navigationBarTitleText": "我的退款银行账户",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}

			},
			{
				"path": "cardBusiness/expenseRecord/record-list",
				"style": {
					"navigationBarTitleText": "消费记录",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "cardBusiness/expenseRecord/record-detail",
				"style": {
					"navigationBarTitleText": "消费详情",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			// {
			// 	"path": "invoiceBusiness/home/<USER>",
			// 	"style": {
			// 		"navigationBarTitleText": "开具发票",
			// 		// "navigationBarBackgroundColor": "#0066E9",
			// 		"navigationBarTextStyle": "black"
			// 	}
			// },
			{
				"path": "invoiceBusiness/invoiceTitle/index",
				"style": {
					"navigationBarTitleText": "我的发票抬头",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/invoiceTitle/addInvoice",
				"style": {
					"navigationBarTitleText": "新增发票抬头",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/invoiceTitle/detail",
				"style": {
					"navigationBarTitleText": "我的发票抬头",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/invoiceRecord/index",
				"style": {
					"navigationBarTitleText": "发票历史",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/invoiceRecord/details",
				"style": {
					"navigationBarTitleText": "发票历史",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/invoiceRecord/send",
				"style": {
					"navigationBarTitleText": "重发发票",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/uninvoiceList/index",
				"style": {
					"navigationBarTitleText": "待开发票原订单",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/uninvoiceList/invoice",
				"style": {
					"navigationBarTitleText": "开具发票",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/uninvoiceList/detail",
				"style": {
					"navigationBarTitleText": "开票详情",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/index",
				"style": {
					"navigationBarTitleText": "我的发票",
					// "navigationBarBackgroundColor": "#0066E9",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/home/<USER>",
				"style": {
					"navigationBarTitleText": "其他消费发票",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/home/<USER>",
				"style": {
					"navigationBarTitleText": "发票服务",
					"navigationBarTextStyle": "black"
			}
			},
			{
				"path": "invoiceBusiness/home/<USER>",
				"style": {
					"navigationBarTitleText": "通行费发票服务",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/bindStub/index",
				"style": {
					"navigationBarTitleText": "通行费发票服务",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollSelect/vehicleList",
				"style": {
					"navigationBarTitleText": "通行费发票服务",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollSelect/addVehicle",
				"style": {
					"navigationBarTitleText": "新增开票车辆",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollInvoiceTitle/index",
				"style": {
					"navigationBarTitleText": "通行费发票抬头管理",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollInvoiceTitle/addTitle",
				"style": {
					"navigationBarTitleText": "新增发票抬头",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollInvoiceTitle/bindVehicle",
				"style": {
					"navigationBarTitleText": "选择关联车辆",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollSelect/tollRecord",
				"style": {
					"navigationBarTitleText": "通行费发票",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollSelect/confirmInvoice",
				"style": {
					"navigationBarTitleText": "确定开票信息",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollSelect/recordDetail",
				"style": {
					"navigationBarTitleText": "消费明细",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollHistory/index",
				"style": {
					"navigationBarTitleText": "开票历史",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollHistory/historyDetail",
				"style": {
					"navigationBarTitleText": "开票详情",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/bindStub/resultPage",
				"style": {
					"navigationBarTitleText": "完成结果页",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollHistory/previewInvoice",
				"style": {
					"navigationBarTitleText": "发票预览",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/tollInvoice/tollHistory/changeConfirm",
				"style": {
					"navigationBarTitleText": "更换发票抬头",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/otherInvoice/invoiceRecord/index",
				"style": {
					"navigationBarTitleText": "其他消费发票",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/otherInvoice/invoiceRecord/confirmInvoice",
				"style": {
					"navigationBarTitleText": "确定开票信息",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/otherInvoice/invoiceRecord/recordDetail",
				"style": {
					"navigationBarTitleText": "消费明细",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/otherInvoice/common/resultPage",
				"style": {
					"navigationBarTitleText": "完成结果页",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/otherInvoice/otherHistory/index",
				"style": {
					"navigationBarTitleText": "开票历史",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/otherInvoice/otherHistory/historyDetail",
				"style": {
					"navigationBarTitleText": "开票详情",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/otherInvoice/otherInvoiceTitle/index",
				"style": {
					"navigationBarTitleText": "其他消费发票抬头管理",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/otherInvoice/otherInvoiceTitle/addTitle",
				"style": {
					"navigationBarTitleText": "新增发票抬头",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "invoiceBusiness/otherInvoice/otherHistory/changeConfirm",
				"style": {
					"navigationBarTitleText": "发票红冲",
					"navigationBarTextStyle": "black"
				}
			},
			{
				"path": "personal/new-login",
				"style": {
					"navigationBarTitleText": "登录账号"
			}
			},
			{
				"path": "signWebview/signWebview",
				"style": {
					"navigationBarTitleText": "查看签署电子协议"
			}
			},
			{
				"path": "signWebview/signTransfer",
				"style": {
					"navigationBarTitleText": "电子协议签署查询"
				}
			},
			{
				"path": "signWebview/signSuccess",
				"style": {
					"navigationBarTitleText": "电子协议签署成功"
				}
			},
			{
				"path": "signWebview/signPreview",
				"style": {
					"navigationBarTitleText": "查看电子协议"
				}
			},
			{
				"path": "rechargeBusiness/benefit-detail/benefit-detail",
				"style": {
					"navigationBarTitleText": "充值活动详情"
				}
			}

		]
	}, {
		"root": "pagesC",
		"pages": [{
				"path": "afterSale/cardBusiness/cardBusiness",
				"style": {
					"navigationBarTitleText": "ETC卡片补领更换申请"
				}

			},
			{
				"path": "afterSale/oubBusiness/obuBusiness",
				"style": {
					"navigationBarTitleText": "OBU补领更换申请"
				}

			},
			{
				"path": "afterSale/loss/loss",
				"style": {
					"navigationBarTitleText": "挂失解挂"
				}
			},
			{
				"path": "afterSale/mailOldDevice/mailOldDevice",
				"style": {
					"navigationBarTitleText": "邮寄旧设备"
				}

			},
			{
				"path": "coupons/selectCoupons/selectCoupons",
				"style": {
					"navigationBarTitleText": "权益中心"
				}

			},
			{
				"path": "coupons/couponsRules/couponsRules",
				"style": {
					"navigationBarTitleText": "使用规则"
				}

			},
			{
				"path": "coupons/benefit/benefit",
				"style": {
					"navigationBarTitleText": "充值权益"
				}
			
			},
			{
				"path": "resetPwd/p-resetPwd",
				"style": {
					"navigationBarTitleText": "忘记密码",
					"mp-weixin": {
						"usingComponents": {
							"t-captcha": "plugin://captcha/t-captcha"
						}

					}
				}
			},
			{
				"path": "afterSale/businessReceipt/businessReceipt",
				"style": {
					"navigationBarTitleText": "业务记录"

				}
			},
			{
				"path": "afterPay/searchInfo/searchInfo",
				"style": {
					"navigationBarTitleText": "待补缴信息查询"

				}
			},
			{
				"path": "afterPay/recordDetail/recordDetail",
				"style": {
					"navigationBarTitleText": "交易详情列表"

				}
			},
			{
				"path": "afterPay/recordDetail/afterPayList",
				"style": {
					"navigationBarTitleText": "补缴记录"

				}
			},
			{
				"path": "afterPay/queryEnter/queryEnter",
				"style": {
					"navigationBarTitleText": "欠费补缴"

				}
			},
			{
				"path": "afterPay/dispute/dispute",
				"style": {
					"navigationBarTitleText": "异议处理"

				}
			},
			{
				"path": "userAgreement/agreementList",
				"style": {
					"navigationBarTitleText": "电子协议"

				}
			},
			{
				"path": "afterSaleBusiness/serviceOrder/index",
				"style": {
					"navigationBarTitleText": "服务订单"

				}
			},
			{
				"path": "afterSaleBusiness/vehicleList/index",
				"style": {
					"navigationBarTitleText": "售后车辆"

				}
			},
			{
				"path": "afterSaleBusiness/home/<USER>",
				"style": {
					"navigationBarTitleText": "设备售后"
				}
			},
			{
				"path": "selfCheck/index",
				"style": {
					"navigationBarTitleText": "设备自助检测"

				}
			},
			{
				"path": "selfCheck/openDeviceBlue",
				"style": {
					"navigationBarTitleText": "设备自助检测"

				}

			},
			{
				"path": "selfCheck/connect",
				"style": {
					"navigationBarTitleText": "设备自助检测"

				}

			},
			{
				"path": "selfCheck/notBlue",
				"style": {
					"navigationBarTitleText": "设备自助检测"

				}

			},
			{
				"path": "selfCheck/result",
				"style": {
					"navigationBarTitleText": "设备自助检测"

				}

			},
			{
				"path": "ccsSign/index",
				"style": {
					"navigationBarTitleText": "次结式产品签约"
				}
			},
			{
				"path": "ccsSign/agreement",
				"style": {
					"navigationBarTitleText": "代收业务委托协议"
				}
			},
			{
				"path": "ccsSign/attorney",
				"style": {
					"navigationBarTitleText": "委托扣款授权书"
				}
			},
			{
				"path": "afterSaleBusiness/applyAfterSale/index",
				"style": {
					"navigationBarTitleText": "售后服务申请"
				}
			}, {
				"path": "afterSaleBusiness/orderDetails/index",
				"style": {
					"navigationBarTitleText": "设备自助激活申请订单"
				}
			},
			{
				"path": "afterSaleBusiness/promptPage/index",
				"style": {
					"navigationBarTitleText": "设备自助激活申请"
				}
			},
			{
				"path": "afterSaleBusiness/orderProgress/index",
				"style": {
					"navigationBarTitleText": "办理进度"
				}
			},
			{
				"path": "afterSaleBusiness/orderDetails/viewDetail",
				"style": {
					"navigationBarTitleText": "设备自助激活申请订单"
				}
			},
			{
				"path": "changeBussiness/orderDetails/index",
				"style": {
					"navigationBarTitleText": "设备更换申请订单"
				}
			},
			{
				"path": "changeBussiness/orderWl/orderWlDetail",
				"style": {
					"navigationBarTitleText": "物流信息"
				}
			},
			{
				"path": "changeBussiness/apply/index",
				"style": {
					"navigationBarTitleText": "设备更换申请"
				}
			},
			{
				"path": "changeBussiness/orderDetails/failPassIndex",
				"style": {
					"navigationBarTitleText": "设备更换申请订单"
				}
			},
			{
				"path": "changeBussiness/orderDetails/modifyDeliveryAddress",
				"style": {
					"navigationBarTitleText": "修改收货地址"
				}
			},
			{
				"path": "changeBussiness/orderDetails/modifyData",
				"style": {
					"navigationBarTitleText": "修改提交资料"
				}
			},
			{
				"path": "changeBussiness/orderDetails/returnLogisticsInfo",
				"style": {
					"navigationBarTitleText": "设备更换申请订单"
				}
			},
			{
				"path": "changeBussiness/orderDetails/pickUp",
				"style": {
					"navigationBarTitleText": "设备更换申请订单"
				}
			},
			{
				"path": "reissueBussiness/apply/index",
				"style": {
					"navigationBarTitleText": "设备补办申请"
				}
			},
			{
				"path": "reissueBussiness/orderDetails/index",
				"style": {
					"navigationBarTitleText": "设备补办申请订单"
				}
			},
			{
				"path": "reissueBussiness/orderDetails/modifyAddress",
				"style": {
					"navigationBarTitleText": "修改收货地址"
				}
			},
			{
				"path": "changeBussiness/orderProgress/index",
				"style": {
					"navigationBarTitleText": "办理进度"
				}
			},
			{
				"path": "infoBusiness/index",
				"style": {
					"navigationBarTitleText": "资讯列表"

				}

			},
			{
				"path": "infoBusiness/detail",
				"style": {
					"navigationBarTitleText": "资讯详情"

				}
            },
			{
				"path": "logoutBussiness/apply/index",
				"style": {
					"navigationBarTitleText": "设备注销"
				}
			},
			{
				"path": "logoutBussiness/apply/apply",
				"style": {
					"navigationBarTitleText": "设备注销申请"
				}
			},
			{
				"path": "logoutBussiness/orderDetail/orderDetail",
				"style": {
					"navigationBarTitleText": "设备注销申请订单"
				}
			},
			{
				"path": "logoutBussiness/orderDetail/orderProgressDetail",
				"style": {
					"navigationBarTitleText": "办理进度"
				}
			},
			{
				"path": "productConver/apply",
				"style": {
					"navigationBarTitleText": "产品转换"
				}
			},
			{
				"path": "productConver/order",
				"style": {
					"navigationBarTitleText": "支付费用"
				}
			},
			{
				"path": "productConver/orderDetail",
				"style": {
					"navigationBarTitleText": "ETC产品转换订单"
				}
			},
			{
				"path": "productConver/converSuccess",
				"style": {
					"navigationBarTitleText": "产品转换"
				}
			},
			{
				"path": "productConver/converSignature",
				"style": {
					"navigationBarTitleText": "产品转换签约"
				}
			},
			{
				"path": "productConver/ccsSign/index",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "免密代扣签约"
				}
			},
			{
				"path": "productConver/ccsSign/attorney",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "委托扣款授权书"
				}
			},
			{
				"path": "productConver/refundAmount",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "余额处理"
				}
			},
			{
				"path": "lost-uncheat/index",
				"style": {
					"navigationBarTitleText": "设备挂失/解挂"
				}
			},
			{
				"path": "lost-uncheat/lostUncheat",
				"style": {
					"navigationBarTitleText": "设备挂失/解挂"
				}
			},
			{
				"path": "lost-uncheat/lost-result",
				"style": {
					"navigationBarTitleText": "挂失/解挂结果"
				}
			},
			{
				"path": "rechargeBenefit/index",
				"style": {
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "OBU质保期延长"
				}
			}

		]
	}, {
		"root": "pagesD",
		"pages": [{
				"path": "home/p-home/p-home",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTextStyle": "white",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "home/all-business/index",
				"style": {
					"navigationBarTitleText": "全部业务"
				}
			},
			{
				"path": "home/all-business/search/index",
				"style": {
					"navigationBarTitleText": "业务搜索"
				}
			},
			{
				"path": "home/all-business/msgList/index",
				"style": {
					"navigationBarTitleText": "消息中心"
				}
			},
			{
				"path": "register/p-register",
				"style": {
					"navigationBarTitleText": "用户注册",
					"mp-weixin": {
						"usingComponents": {
							"t-captcha": "plugin://captcha/t-captcha"
						}
					}
				}
			},
			{
				"path": "login/accountLogin",
				"style": {
					"navigationBarTitleText": "账号登录",
					"mp-weixin": {
						"usingComponents": {
							"t-captcha": "plugin://captcha/t-captcha"
						}
					}
				}
			},
			{
				"path": "login/p-login",
				"style": {
					"navigationBarTitleText": "微信登录"
				}
			},
			{
				"path": "travelService/jam-info",
				"style": {
					"navigationBarTitleText": "桂小通(广西捷通)"
				}
			},
			{
				"path": "travelService/rescue/index",
				"style": {
					"navigationBarTitleText": "一键救援"
				}
			},
			{
				"path": "travelService/rescue/rescue-order",
				"style": {
					"navigationBarTitleText": "我的订单"
				}
			},
			{
				"path": "travelService/rescue/order-detail",
				"style": {
					"navigationBarTitleText": "订单详情"
				}
			},
			{
				"path": "travelService/information/index",
				"style": {
					"navigationBarTitleText": "高速资讯"
				}
			},
			{
				"path": "star/index",
				"style": {
					"navigationBarTitleText": "我的收藏"
				}
			},
			{
				"path": "travelService/serviceArea/detail",
				"style": {
					"navigationBarTitleText": "服务区详情"
				}
			},
			{
				"path": "charging/index",
				"style": {
					"navigationBarTitleText": "充电服务"
				}
			},
			{
				"path": "charging/chargingDetail/index",
				"style": {
					"navigationBarTitleText": "充电桩详情"
				}
			},
			{
				"path": "msgSubscribe/index",
				"style": {
					"navigationBarTitleText": "订阅详情"
				}
			},
			{
				"path": "msgSubscribe/chooseCar",
				"style": {
					"navigationBarTitleText": "选择车辆"
				}
			},
			{
				"path": "msgSubscribe/reminder-line",
				"style": {
					"navigationBarTitleText": "充值提醒线修改"
				}
			},
			{
				"path": "evaluationPage/index",
				"style": {
					"navigationBarTitleText": "用户评价"
				}
			},
			{
				"path": "evaluationPage/commentList",
				"style": {
					"navigationBarTitleText": "用户评价列表"
				}
			},
			{
				"path": "travelService/tollStation/index",
				"style": {
					"navigationBarTitleText": "收费站列表"
				}
			},
			{
				"path": "travelService/tollStation/detail",
				"style": {
					"navigationBarTitleText": "收费站详情"
				}
			},
			{
				"path": "agent/index",
				"style": {
					"navigationBarTitleText": "小通AI智能出行"
				}
			}
		]
	}],
	"navigateToMiniProgramAppIdList": [
		"wx9040bb0d3f910004",
		"wx6412b2560c6b1aa9",
		"wx07e128202169f81e"
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "桂小通(广西捷通)",
		"navigationBarBackgroundColor": "#FFF",
		"backgroundColor": "#F8F8F8"
	},
	"permission": {
		"scope.userLocation": {
			"desc": "你的位置信息将用于小程序获取省份信息"
		}
	},
	"preloadRule": {
		"pages/home/<USER>/p-home": {
			"network": "all",
			"packages": ["__APP__"]
		}
	}
}