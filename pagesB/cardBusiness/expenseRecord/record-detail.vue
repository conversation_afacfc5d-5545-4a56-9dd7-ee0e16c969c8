<template>
	<view class="detail-content">
		<view class="block"></view>
		<view class="content">
			<view class="location" v-if="detail.scene == 2">{{ detail.parkingName }}</view>
			<view class="location" v-if="detail.scene == 3">{{ detail.oilStationName }}</view>
			<view class="park-lot" v-if="detail.scene == 1">
				<view class="toll-station" style="margin-right: 30rpx;">
					<view class="toll-station-name toll-station-name-en" :class="{ hide: enOverflow }"
						:style="{ height: (exOverflow && !enOverflow ? '40px' : 'auto') }" @click.stop="showEn()"
						ref="enStation">{{ detail.enStation }}</view>
					<view class="tooltip" v-if="showToolTipEn">
						{{ detail.enStation }}
					</view>
					<view class="stand">收费站</view>
					<view class="charge-time">{{ detail.enTime }}</view>
				</view>
				<image src="../../static/to.png" mode="" class="to"></image>
				<view class="toll-station" style="margin-left: 30rpx;">
					<view class="toll-station-name toll-station-name-ex" :class="{ hide: exOverflow }"
						:style="{ height: (enOverflow && !exOverflow ? '40px' : 'auto') }" @click.stop="showEx()">
						{{ detail.exStation }}
					</view>
					<view class="tooltip" v-if="showToolTipEx">
						{{ detail.exStation }}
					</view>
					<view class="stand">收费站</view>
					<view class="charge-time">{{ detail.exTime }}</view>
				</view>
			</view>
			<view class="recharge">￥{{ moneyFilter(detail.deductionAmount) }}</view>
			<view class="margin-40 dash-line"></view>
			<view v-if="detail.scene == 1">
				<view class="exhibition">
					<text class="title">应付金额</text><text class="value">￥{{ moneyFilter(detail.payAmount) }}</text>
				</view>
				<view class="exhibition">
					<text class="title">优惠金额</text><text class="value">-￥{{ moneyFilter(detail.discountAmount) }}</text>
				</view>
				<view class="exhibition">
					<text class="title">实付金额</text><text class="value">￥{{ moneyFilter(detail.deductionAmount) }}</text>
				</view>
				<view class="exhibition" v-if="detail.isOverdueAmount">
					<text class="title">服务费</text><text class="value">￥{{ moneyFilter(detail.serviceAmount) }}</text>
				</view>
				<view class="exhibition" v-if="detail.isOverdueAmount">
					<text class="title">逾期服务费</text><text class="value">￥{{ moneyFilter(detail.overdueAmount) }}</text>
				</view>
				<view class="exhibition" v-if="detail.isOverdueAmount">
					<text class="title">逾期天数</text><text class="value">{{ detail.overdueDay }}天</text>
				</view>
				<view class="margin-40 dash-line"></view>
				<view class="exhibition">
					<text class="title">车牌号码</text><text class="value">{{ detail.carNo }}[{{
						plateColorToColorMap.get(detail.carColor + '') }}]</text>
				</view>
				<view class="exhibition">
					<text class="title">ETC卡号</text><text class="value">{{ maskCardObu(detail.cardNo) }}</text>
				</view>
				<view class="exhibition">
					<text class="title">车生活场景</text><text class="value">{{ detail.sceneStr }}</text>
				</view>
				<view class="margin-40 dash-line"></view>
				<view class="exhibition">
					<text class="title">支付方式</text><text class="value">{{ detail.payTypeName }}</text>
				</view>
				<view class="exhibition">
					<text class="title">支付状态</text><text class="value">{{ filterDict(detail.payStatus) }}</text>
				</view>
				<view class="exhibition">
					<text class="title">支付时间</text><text class="value">{{ detail.payTime }}</text>
				</view>
			</view>
			<view v-if="detail.scene == 2">
				<view class="exhibition" v-if="detail.isOverdueAmount">
					<text class="title">滞纳金</text><text class="value">￥{{ moneyFilter(detail.overdueAmount) }}</text>
				</view>
				<view class="exhibition" v-if="detail.isOverdueAmount">
					<text class="title">滞纳天数</text><text class="value">{{ detail.overdueDay }}天</text>
				</view>
				<view class="margin-40 dash-line"></view>
				<view class="exhibition">
					<text class="title">车牌号码</text><text class="value">{{ detail.carNo }}[{{
						plateColorToColorMap.get(detail.carColor + '') }}]</text>
				</view>
				<view class="exhibition">
					<text class="title">ETC卡号</text><text class="value">{{ maskCardObu(detail.cardNo) }}</text>
				</view>
				<view class="exhibition">
					<text class="title">车生活场景</text><text class="value">{{ detail.sceneStr }}</text>
				</view>
				<view class="exhibition">
					<text class="title">停车时长</text><text class="value">{{ detail.parkingTimeInterval }}</text>
				</view>
				<view class="exhibition">
					<text class="title">出场时间</text><text class="value">{{ detail.outTime }}</text>
				</view>
				<view class="margin-40 dash-line"></view>
				<view class="exhibition">
					<text class="title">支付方式</text><text class="value">{{ detail.payTypeName }}</text>
				</view>
				<view class="exhibition">
					<text class="title">支付状态</text><text class="value">{{ filterDict(detail.payStatus) }}</text>
				</view>
				<view class="exhibition">
					<text class="title">支付时间</text><text class="value">{{ detail.payTime }}</text>
				</view>
			</view>
			<view v-if="detail.scene == 3">
				<view class="margin-40 dash-line"></view>
				<view class="exhibition">
					<text class="title">车牌号码</text><text class="value">{{ detail.carNo }}[{{
						plateColorToColorMap.get(detail.carColor + '') }}]</text>
				</view>
				<view class="exhibition">
					<text class="title">ETC卡号</text><text class="value">{{ maskCardObu(detail.cardNo) }}</text>
				</view>
				<view class="exhibition">
					<text class="title">车生活场景</text><text class="value">{{ detail.sceneStr }}</text>
				</view>
				<view class="exhibition">
					<text class="title">交易时间</text><text class="value">{{ detail.oilTime }}</text>
				</view>
				<view class="margin-40 dash-line"></view>
				<view class="exhibition">
					<text class="title">支付方式</text><text class="value">{{ detail.payTypeName }}</text>
				</view>
				<view class="exhibition">
					<text class="title">支付状态</text><text class="value">{{ filterDict(detail.payStatus) }}</text>
				</view>
				<view class="exhibition">
					<text class="title">支付时间</text><text class="value">{{ detail.payTime }}</text>
				</view>
			</view>
			<template v-if="refundModel.length > 0">
				<view v-for="(item, index) in refundModel" :key="index">
					<view class="margin-40 dash-line"></view>
					<view class="exhibition">
						<text class="title">退款方式</text><text class="value">{{ item.refundChannel }}</text>
					</view>
					<view class="exhibition">
						<text class="title">退款状态</text><text class="value">{{ refundFilterDict(item.refundStatus)
						}}</text>
					</view>
					<view class="exhibition">
						<text class="title">退款时间</text><text class="value">{{ item.refundTime }}</text>
					</view>
					<view class="exhibition">
						<text class="title">退款金额</text><text class="value">￥{{ moneyFilter(item.refundAmount) }}</text>
					</view>
				</view>
				<view class="button-bank" v-if="showBtn" @click="toBankAccount()">更新收款银行账户信息
				</view>
			</template>
		</view>
		<!-- #ifdef MP-WEIXIN -->
		<view class="button" v-if="detail.payStatus == '3'" @click="supplementaryPayment()">去补缴</view>
		<!-- #endif -->
		<view class="evaluation-container" v-if="shouldShowEvaluation">
			<evaluation businessType="etcConsumption" :initialStatus="evaluationStatus" :initialRate="evaluationRate"
				:initialText="evaluationText" :initialImgList="evaluationImgList" :initialTime="evaluationTime"
				@status-change="updateStatus" @rate-change="updateRate" @submit="handleEvaluationSubmit" />
		</view>
	</view>
</template>

<script>
import {
	plateColorToColorMap
} from '@/common/systemConstant.js';
import evaluation from '@/pagesB/components/evaluation/index.vue';
import { getOpenid, getOpenidForRead, getLoginUserInfo } from '@/common/storageUtil.js';
import {
	maskCardObu
} from '@/common/util.js';

export default {
	data() {
		return {
			plateColorToColorMap,
			id: '',
			detail: {},
			showToolTipEn: false,
			showToolTipEx: false,
			enOverflow: false, //是否溢出
			exOverflow: false, //是否溢出
			showBtn: false,
			refundModel: [], //退款记录
			// 评价相关数据
			evaluationMode: 'auto', // 评价模式：show, hide, auto
			showEvaluation: false, // 是否显示评价组件
			evaluationStatus: 'unevaluated', // 评价状态：unevaluated, evaluating, evaluated
			evaluationRate: 0, // 评价评分
			evaluationText: '', // 评价内容
			evaluationImgList: [], // 评价图片列表
			evaluationTime: '', // 评价时间
			evaluationLoading: false, // 评价加载状态
			orderId: '', // 订单号
			evaluationData: null, // 已有评价数据
			hasEvaluationData: false // 是否有评价数据
		}
	},
	components: {
		evaluation
	},
	async onLoad(options) {
		this.id = options.id
		this.orderId = this.id

		let { data } = await this.$request.post(this.$interfaces.getVehicleConsume, {
			data: {
				id: this.id
			}
		})

		const record = data.consumeViewList.records[0];
		let detail = {};
		let sourceView = {};

		if (record.scene == 1) { //高速
			sourceView = record.vehiclePassView || {};
		} else if (record.scene == 2) { //停车场
			sourceView = record.vehicleParkingView || {};
		} else if (record.scene == 3) { //加油站
			sourceView = record.vehicleStationView || {};
		}

		detail = { ...sourceView };
		detail.sceneStr = sourceView.scene;
		detail.scene = record.scene;
		this.detail = detail

		// 处理评价模式参数
		if (options.evaluationMode) {
			this.evaluationMode = options.evaluationMode;
		}
		// 如果有评价显示标识，直接设置为显示
		if (options.showEvaluation === 'true' || options.showEvaluation === true) {
			this.evaluationMode = 'show';
		}
		console.log(this.evaluationMode, 'evaluationMode');

		this.refundModel = this.detail.refundModel || []
		if (this.refundModel.length > 0) {
			//退款失败的时候需要显示联系人页面按钮
			let filter = this.refundModel.filter(item => {
				return item.refundStatus == '3'
			})
			if (filter.length > 0) {
				this.showBtn = true
			} else {
				this.showBtn = false
			}
		}

		// 查询评价数据
		if (this.orderId && this.shouldShowEvaluation) {
			this.queryEvaluationByOrderId();
		}

		this.$nextTick(function () {
			this.judgmentOverflow()
		})
	},
	computed: {
		// 判断是否显示评价组件
		shouldShowEvaluation() {
			// 首先检查是否允许评论，如果 isAllowComment 为 0，则不显示评价组件 加油站场景（scene=3）不允许评价
			if (this.detail.isAllowComment == 0 || this.detail.scene == 3) {
				return false;
			}

			// 根据评价模式决定
			switch (this.evaluationMode) {
				case 'show':
					return true;
				case 'hide':
					return false;
				case 'auto':
					// 自动判断：只有支付成功或补缴成功时才显示评价
					return this.detail.payStatus === '1' || this.detail.payStatus === '4';
				default:
					// 默认显示评价组件
					return this.detail.payStatus === '1' || this.detail.payStatus === '4';
			}
		}
	},
	methods: {
		maskCardObu,
		// 解析图片字符串为URL数组
		parseImageString(imageStr) {
			if (!imageStr || typeof imageStr !== 'string') {
				return [];
			}

			// 按逗号分割并过滤空字符串
			return imageStr.split(',')
				.map(url => url.trim())
				.filter(url => url.length > 0);
		},

		// 查询评价数据
		async queryEvaluationByOrderId() {
			if (!this.orderId) {
				console.warn('订单号为空，无法查询评价');
				return;
			}

			try {
				this.evaluationLoading = true;
				const params = {
					orderNo: this.orderId
				};

				const response = await this.$request.post(this.$interfaces.getEtcEvaluationList, {
					data: params
				});

				if (response.code === 200 && response.data) {
					const evaluationList = response.data;

					if (evaluationList.length > 0) {
						// 取最新的评价记录
						const latestEvaluation = evaluationList[0];
						this.evaluationData = latestEvaluation;
						this.hasEvaluationData = true;
						// 设置评价组件的显示数据
						this.evaluationStatus = 'evaluated';
						this.evaluationRate = latestEvaluation.star || 0;
						this.evaluationText = latestEvaluation.content || '';
						this.evaluationTime = latestEvaluation.reviewsTime || '';
						this.evaluationImgList = this.parseImageString(latestEvaluation.image || '');

					} else {
						// 没有评价数据
						this.hasEvaluationData = false;
						this.evaluationStatus = 'unevaluated';
						this.evaluationRate = 0;
						this.evaluationText = '';
						this.evaluationTime = '';
						this.evaluationImgList = [];
					}
				} else {
					// 没有评价数据
					this.hasEvaluationData = false;
					this.evaluationStatus = 'unevaluated';
					this.evaluationRate = 0;
					this.evaluationText = '';
					this.evaluationTime = '';
					this.evaluationImgList = [];
				}
			} catch (error) {
				console.error('查询评价数据失败:', error);
				// 接口返回失败或无数据
				this.hasEvaluationData = false;
				this.evaluationStatus = 'unevaluated';
			} finally {
				this.evaluationLoading = false;
			}
		},

		judgmentOverflow() {
			// 只在高速通行场景下检测溢出
			if (this.detail.scene !== 1) {
				return;
			}

			try {
				let query = wx.createSelectorQuery().in(this);

				// 检测入口收费站名称溢出
				query.select('.toll-station-name-en').boundingClientRect(data => {
					if (data && data.height && data.height > 40) {
						this.enOverflow = true;
					}
				}).exec();

				// 检测出口收费站名称溢出
				query.select('.toll-station-name-ex').boundingClientRect(data => {
					if (data && data.height && data.height > 40) {
						this.exOverflow = true;
					}
				}).exec();
			} catch (error) {
				console.warn('判断文本溢出时发生错误:', error);
			}
		},

		showEn() {
			if (!this.enOverflow) {
				return
			}
			this.showToolTipEn = !this.showToolTipEn
		},

		showEx() {
			if (!this.exOverflow) {
				return
			}
			this.showToolTipEx = !this.showToolTipEx
		},
		filterDict(val) {
				let payStatusObj = {
					"0": '扣款中',
					"1": '扣款成功',
					"2": '扣款失败',
					"3": '待补缴',
					"4": '补缴成功',
					"9": '月底统一扣款'
				}
				return payStatusObj[val] || ''
			},
			refundFilterDict(val) {
				let refundStatusObj = {
					"0": '待退款',
					"1": '退款中',
					"6": '退款中',
					"2": '退款成功',
					"5": '退款成功',
					"3": '退款异常',
				}
				return refundStatusObj[val] || ''
			},

		moneyFilter(val) {
			let value = val;
			if (value == 0) return value;
			value = value / 100;
			return this.toDecimal2(value);
		},
		toDecimal2(x) {
			var f = parseFloat(x);
			if (isNaN(f)) {
				return false;
			}
			var f = Math.round(x * 100) / 100;
			var s = f.toString();
			var rs = s.indexOf(".");
			if (rs < 0) {
				rs = s.length;
				s += ".";
			}
			while (s.length <= rs + 2) {
				s += "0";
			}
			return s;
		},

		toBankAccount() {
			uni.navigateTo({
				url: '/pagesB/disputeRefund/accountList'
			})
		},

		supplementaryPayment() {
			let nextData = {
				vehicle_code: this.detail.carNo,
				vehicle_color: this.detail.carColor,
			}
			uni.navigateTo({
				url: '/pagesC/afterPay/recordDetail/recordDetail?nextData=' + encodeURIComponent(JSON
					.stringify(nextData))
			})
			return;
		},

		// 处理评价提交
		async handleEvaluationSubmit(evaluationData) {
			try {
				uni.showLoading({
					title: '提交评价中...'
				});

				// 获取用户openid
				const openid = getOpenid() || getOpenidForRead() || '';
				if (!openid) {
					uni.showToast({
						title: '用户信息获取失败，请重新登录',
						icon: 'error'
					});
					return;
				}

				// 构造API调用参数
				const params = {
					openid: openid,
					content: evaluationData.text || '',
					star: evaluationData.rate || 0,
					image: evaluationData.urlList ? evaluationData.urlList.join(',') : '',
					orderNo: this.orderId,
					// 根据场景确定消费类型
					consumptionType: this.detail.scene === 1 ? 1 : 2, // 1-高速消费 2-ETC停车消费
					vehiclePlate: this.detail.carNo || '',
					vehiclePlateColor: this.detail.carColor || '0',
					amount: this.detail.deductionAmount || 0,
					carType: this.detail.carType,
					isTruck: this.detail.isTruck,
					entranceTime: this.detail.enTime,
					exitTime: this.detail.exTime,
					netUserId: getLoginUserInfo().userIdStr,
				};

				// 根据场景添加特定字段
				if (this.detail.scene === 1) {
					// 高速消费
					params.entranceCode = this.detail.enStationCode || '';
					params.entranceName = this.detail.enStation || '';
					params.exitCode = this.detail.exStationCode || '';
					params.exitName = this.detail.exStation || '';
				}

				// 调用评价提交接口
				const response = await this.$request.post(this.$interfaces.addEtcEvaluation, {
					data: params
				});

				if (response.code === 200) {
					uni.showToast({
						title: '评价提交成功',
						icon: 'success'
					});

					// 重新查询评价数据以更新状态
					await this.queryEvaluationByOrderId();
				} else {
					uni.showModal({
						title: '评价提交失败',
						content: response.msg,
						showCancel: false
					})
				}
			} catch (error) {
				uni.showModal({
					title: '评价提交失败',
					content: error.message || '网络错误',
					showCancel: false
				})
			} finally {
				uni.hideLoading();
			}
		},

		// 更新评价状态
		updateStatus(status) {
			this.evaluationStatus = status;
		},

		// 更新评价评分
		updateRate(rate) {
			this.evaluationRate = rate;
		}
	}
}
</script>

<style lang="scss" scoped>
.detail-content {
	padding-bottom: 50rpx;

	.content {
		width: calc(100% - 60rpx);
		margin: 0 30rpx 0;
		background-color: #fff;
		border-radius: 10rpx;
		padding-bottom: 20rpx;

		.location {
			font-size: 32rpx;
			font-weight: 500;
			color: #333333;
			line-height: 39rpx;
			text-align: center;
			padding-top: 60rpx;
		}

		.park-lot {
			display: flex;
			padding-top: 60rpx;
			margin: 0 30rpx;

			.toll-station {
				width: 40%;
				text-align: center;
				position: relative;

				.toll-station-name {
					min-height: 39rpx;
					font-size: 32rpx;
					font-weight: 500;
					color: #333333;
					line-height: 20px;
				}

				.hide {
					word-break: break-word;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}

				.tooltip {
					width: 120px;
					height: auto;
					background: #000000;
					opacity: 0.69;
					padding: 8px;
					position: relative;
					font-size: 13px;
					border-radius: 6px;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 15px;
					position: absolute;
					top: 95rpx;
					left: 0;
					z-index: 999;
				}

				.tooltip::before {
					content: '';
					width: 0;
					height: 0;
					border-width: 8px;
					border-color: transparent transparent #000 transparent;
					border-style: dashed dashed solid dashed;
					position: absolute;
					top: -16px;
					left: 45%;
				}

				.stand {
					height: 39rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;
					line-height: 39rpx;
					margin: 6rpx 0;
				}

				.charge-time {
					font-size: 24rpx;
					height: 33rpx;
					font-weight: 400;
					color: #666666;
					line-height: 33rpx;
				}
			}

			.to {
				width: 67rpx;
				height: 18rpx;
				margin-top: 26rpx;
				background-size: 100%;
			}
		}

		.recharge {
			height: 62rpx;
			font-size: 41rpx;
			font-weight: 500;
			color: #EA3030;
			line-height: 62rpx;
			text-align: center;
			margin-top: 30rpx;
		}

		.dash-line {
			background-image: linear-gradient(to right, #c3c3c3 0%, #c3c3c3 50%, transparent 50%);
			background-size: 10px 1px;
			background-repeat: repeat-x;
			width: calc(100% - 40rpx);
			margin-left: 20rpx;
			height: 1px;
		}

		.margin-40 {
			margin: 40rpx 20rpx;
		}

		.exhibition {
			margin: 15rpx 30rpx;
			width: 100%;
		}

		.title {
			font-size: 26rpx;
			font-weight: 400;
			color: #888888;
			width: 156rpx;
			display: inline-block;
			margin-right: 70rpx;
		}

		.value {
			font-size: 26rpx;
			font-weight: 400;
			color: #333333;
			display: inline-block;
			width: 420rpx;
		}
	}

	.button {
		width: calc(100% - 60rpx);
		height: 96rpx;
		background: #0066E9;
		border-radius: 10rpx;
		text-align: center;
		font-size: 34rpx;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 96rpx;
		margin: 38rpx 0 30rpx 30rpx;
	}

	.button-bank {
		height: 60rpx;
		background: #0066E9;
		border-radius: 10rpx;
		text-align: center;
		font-size: 28rpx;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 60rpx;
		margin: 30rpx 30rpx 0 30rpx;
	}
}

.evaluation-container {
	margin: 20rpx;
}
</style>
