<template>
	<view class="sellPay-container">
		<view class="block"></view>
		<view class="sellPay-Info search">
			<!-- 按车牌查询模式顶部信息 -->
			<view v-if="queryMode === 'vehicle'" class="search-form-group highlight-info">
				<view class="title">车牌号码</view>
				<view class="value">{{primaryVehicle.vehicleCode}}（{{getVehicleColor(primaryVehicle.vehicleColor)}}）</view>
			</view>
			
			<!-- 按ETC卡查询模式顶部信息 -->
			<view v-if="queryMode === 'etcCard'" class="search-form-group highlight-info">
				<view class="title">ETC卡号</view>
				<view class="value">{{maskCardObu(primaryEtcCard.cardNo)}}</view>
			</view>

			<!-- ETC卡号多选（按车牌查询模式） -->
			<view v-if="queryMode === 'vehicle'">
				<multi-selector
					title="ETC卡号"
					placeholder="请选择ETC卡号"
					:options="etcCardOptions"
					v-model="selectedEtcCards"
					value-key="cardNo"
					label-key="displayText"
					@change="onEtcCardChange"
				></multi-selector>
			</view>
			
			<!-- 按ETC卡查询模式：显示关联的车牌号（只读） -->
			<view v-if="queryMode === 'etcCard'" class="search-form-group">
				<view class="title">车牌号码</view>
				<view class="value">{{primaryEtcCard.vehicleCode || '暂无关联车牌'}}</view>
			</view>

			<!-- 交易场景多选 -->
			<multi-selector
				title="交易场景"
				placeholder="请选择交易场景"
				:options="sceneOptions"
				v-model="selectedScenes"
				value-key="value"
				label-key="label"
				@change="onSceneChange"
			></multi-selector>
			<view class="search-form-item" style="display: flex;">
				<view class="title">交易开始时间</view>
				<view class="picker">
					<view class="pick-date pick-date-one">
						<picker mode="date" fields="month" @change="startDateChange" :value="strartTime"
							:start="startDate" :end="endTime" style="width: 100%;">
							<u-cell-item title=" " :arrow="false" icon="date-fill">
								<view class="monthData">{{strartTime}}</view>
							</u-cell-item>
						</picker>
					</view>
					<view style="margin: 0 30rpx;">至</view>
					<view class="pick-date pick-date-two">
						<picker mode="date" fields="month" @change="endDateChange" :value="endTime" :end="endDate"
							style="width: 100%;">
							<u-cell-item title=" " :arrow="false" icon="date-fill">
								<view class="monthData">{{endTime}}</view>
							</u-cell-item>
						</picker>
					</view>
				</view>
			</view>
			<view class="search-btn">
				<button class="cu-btn bg-topic" @tap="searchTable">查询</button>
			</view>
		</view>
		<view class="total">
			<view class="total-recharge">消费总金额：<span>{{moneyFilter(totalAmount)}}元</span></view>
			<view class="frequency">消费次数：<span>{{totalCount}}次</span></view>
		</view>
		<scroll-view :style="{height:height}" :scroll-top="scrollTop" scroll-y="true" class="scroll-Y"
			:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
			<view class="card-Info">
				<view class="card" v-for="(item, index) in currentConsumeList" :key="index">
					<view class="card-top">
						<image :src="'../../static/pic'+item.scene+'.png'" mode="" class="icon"></image>
						<view class="deduction">实付金额<span class="unit">￥</span>
							<span class="money">{{moneyFilter(item.deductionAmount)}}</span>
						</view>
						<view class="status" :class="'status'+item.payStatus">
							<view class="name" :class="'name'+item.payStatus">{{filterDict(item.payStatus)}}</view>
						</view>
					</view>
					
					<!-- 加油站 -->
					<view class="card-middle" v-if="item.scene==3">
						<view>{{item.oilStationName}} {{item.oilTime}}</view>
					</view>
					<!-- 停车场 -->
					<view class="card-middle" v-if="item.scene==2">
						<view>{{item.parkingName}}</view>
					</view>
					<view class="card-middle" v-if="item.scene==2">
						<view>出场时间：{{item.outTime}}</view>
					</view>
					<view class="card-middle" v-if="item.scene==2">
						<view>交易时间：{{item.transactionTime}}</view>
					</view>
					<!-- 服务区 -->
					<view class="card-middle" v-if="item.scene==1">
						<view>入口：{{item.enStation}} {{item.enTime}}</view>
					</view>
					<view class="card-middle" v-if="item.scene==1">
						<view>出口：{{item.exStation}} {{item.exTime}}</view>
					</view>
					<!-- 根据查询模式显示车牌或ETC卡号 -->
					<view class="card-etc-info card-middle">
						<!-- 按ETC卡查询模式：显示车牌号 -->
						<view class="etc-card-no" v-if="queryMode === 'etcCard'">交易车牌：{{item.carNo || '--'}}</view>
						<!-- 按车牌查询模式：显示ETC卡号 -->
						<view class="etc-card-no" v-else>ETC卡号：{{maskCardObu(item.etcCardNo || item.cardNo) || '--'}}</view>
					</view>
					<view class="dash-line"></view>
					<view class="card-btn">
						<view class="btn" @click="goDetail(item)">详情</view>
						<!-- #ifdef MP-WEIXIN -->
						<view class="btn btn1" v-if="item.payStatus == 3" @click="supplementaryPayment()">去补缴</view>
						<!-- #endif -->
					</view>
				</view>
			</view>
			<view v-if="totalCount==0" class="no-data">
				<image src="../../static/no_data.png" mode="" class="no-data-img"></image>
				<view class="no-data-title">暂无交易记录</view>
			</view>
		</scroll-view>

		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import TButton from "@/components/t-button.vue";
	import tLoading from '@/components/common/t-loading.vue';
	import loadMore from '../../components/load-more/index.vue';
	import MultiSelector from './components/multi-selector.vue';
	import {
		getAccountId
	} from "@/common/storageUtil.js";
	import {
		getVehicleColor
	} from '@/common/method/filter.js'
	import {
		maskCardObu
	} from '@/common/util.js'
	import {
		sceneType
	} from "@/common/const/optionData.js"
	var dayjs = require('@/js_sdk/dayjs/dayjs.min.js')

	function getDate(type) {
		const date = new Date();
		let year = date.getFullYear();
		let month = date.getMonth() + 1;
		let day = date.getDate();

		if (type === 'start') {
			year = year - 1;
		} else if (type === 'end') {
			year = year;
		}
		month = month > 9 ? month : '0' + month;
		day = day > 9 ? day : '0' + day;

		return `${year}-${month}`;
	}
	export default {
		components: {
			TButton,
			tLoading,
			loadMore,
			MultiSelector
		},
		data() {
			return {
				isLoading: false,
				// 查询模式相关
				queryMode: 'vehicle', // 'vehicle' | 'etcCard'
				primaryVehicle: {}, // 主要车辆信息
				primaryEtcCard: {}, // 主要ETC卡信息 - 简化结构：{cardNo, cardType, vehicleCode, vehicleColor}
				
				// 多选相关
				selectedEtcCards: [], // 选中的ETC卡
				selectedScenes: [], // 选中的交易场景
				etcCardOptions: [], // ETC卡选项
				sceneOptions: [], // 交易场景选项
				
				height: 'calc(100% - 540rpx)',
				scrollTop: 0,
				lowerThreshold: 120,
				currentConsumeList: [],
				old: {
					scrollTop: 0
				},
				totalAmount: '22',
				totalCount: 3,
				formData: {
					carNos: [], // 车牌号数组（按车牌查询时可能有多个ETC卡）
					etcCardNos: [], // ETC卡号数组（按ETC卡查询时只有一个卡号）
					scenes: [], // 交易场景数组支持多选
					strartTime: '',
					endTime: '',
					pageNum: 1,
					pageSize: 10
				},
				strartTime: '',
				endTime: '',
				flag: false,
				startDate: getDate('start'),
				endDate: getDate('end')
			};
		},

		computed: {
			// 移除缓存依赖，使用传参数据
		},
		onLoad(options) {
			// 解析查询参数
			if (options.queryParams) {
				try {
					const queryParams = JSON.parse(decodeURIComponent(options.queryParams));
					this.queryMode = queryParams.queryMode || 'vehicle';
					console.log(queryParams,'queryParams');
					
					if (queryParams.primaryVehicle) {
						this.primaryVehicle = queryParams.primaryVehicle;
					}
					if (queryParams.primaryEtcCard) {
						this.primaryEtcCard = queryParams.primaryEtcCard;
					}
				} catch (error) {
					console.error('解析查询参数失败:', error);
				}
			}

			this.$nextTick(() => {
				this.initializeData();
			});
		},
		methods: {
			getVehicleColor,
			getAccountId,
			maskCardObu,
			
			// 初始化数据
			initializeData() {
				// 初始化时间
				this.strartTime = dayjs(new Date().getTime() - 30 * 24 * 60 * 60 * 1000).format('YYYY-MM');
				this.formData.strartTime = this.strartTime;
				this.endTime = dayjs(new Date().getTime()).format('YYYY-MM');
				this.formData.endTime = this.endTime;
				
				// 初始化交易场景选项
				this.initSceneOptions();
				
				// 根据查询模式初始化不同数据
				if (this.queryMode === 'vehicle') {
					this.initVehicleMode();
				} else if (this.queryMode === 'etcCard') {
					this.initEtcCardMode();
				}
			},
			
			// 初始化交易场景选项
			initSceneOptions() {
				this.sceneOptions = [
					{ value: '', label: '全部' },
					{ value: '1', label: '高速消费' },
					{ value: '2', label: '停车场' },
					{ value: '3', label: '加油站' }
				];
				// 默认选择全部
				this.selectedScenes = [this.sceneOptions[0]];
			},
			
			// 初始化车牌查询模式
			initVehicleMode() {
				if (!this.primaryVehicle.vehicleCode) {
					console.error('initVehicleMode: 缺少主要车辆信息，无法初始化车牌查询模式');
					uni.showModal({
						title: '提示',
						content: '缺少车辆信息，请重新选择车辆',
						showCancel: false
					});
					return;
				}
				
				// 获取该车辆关联的ETC卡列表
				this.getVehicleEtcCards();
			},
			
			// 初始化ETC卡查询模式
			initEtcCardMode() {
				if (!this.primaryEtcCard.cardNo) {
					console.error('initEtcCardMode: 缺少主要ETC卡信息，无法初始化ETC卡查询模式');
					uni.showModal({
						title: '提示',
						content: '缺少ETC卡信息，请重新选择',
						showCancel: false
					});
					return;
				}

				// 先更新表单数据，再执行查询
				this.updateFormData();
				this.getVehicleConsume();
			},
			
			// 获取车辆关联的ETC卡列表
			getVehicleEtcCards() {
				// 使用传递的cardList数据构建ETC卡选择器
				if (this.primaryVehicle.cardList && this.primaryVehicle.cardList.length > 0) {
					this.etcCardOptions = this.primaryVehicle.cardList.map(card => {
						// 判断是否为当前在用的卡（只有在用的卡才是主卡）
						const isCurrentCard = card.deleteFlag !== '1'

						// 基于deleteFlag映射到组件期望的状态值
						let status = 'active'; // 默认状态
						if (card.deleteFlag === '1') {
							status = 'replaced'; // 更换/注销
						} else if (isCurrentCard) {
							status = 'current'; // 当前在用
						}

						// 生成显示文本（只包含卡号，不包含状态信息）
						const displayText = maskCardObu(card.cardNo);

						return {
							cardNo: card.cardNo,
							displayText: displayText,
							status: status,
							issueTime: card.issueTime || '',
							isCurrentCard: isCurrentCard,
							deleteFlag: card.deleteFlag
						};
					})
				} else {
					// 如果没有cardList数据，使用主卡信息创建单个选项
					this.etcCardOptions = [
						{
							cardNo: this.primaryVehicle.cardNo,
							displayText: maskCardObu(this.primaryVehicle.cardNo),
							status: 'current',
							issueTime: '',
							isCurrentCard: true
						}
					];
				}
				
				// 默认选择当前在用的ETC卡，如果没有则选择第一张卡
				const currentCard = this.etcCardOptions.find(card => card.isCurrentCard);
				this.selectedEtcCards = currentCard ? [currentCard] : [this.etcCardOptions[0]];
				
				// 执行查询
				this.getVehicleConsume();
			},
			

			
			// ETC卡选择变化事件
			onEtcCardChange(selectedCards) {
				// 检查选择是否为空，如果为空则自动选择当前在用的ETC卡
				if (!selectedCards || selectedCards.length === 0) {
					console.log('ETC卡选择为空，自动选择当前在用卡号');
					// 查找当前在用的ETC卡
					const currentCard = this.etcCardOptions.find(card => card.isCurrentCard);
					if (currentCard) {
						this.selectedEtcCards = [currentCard];
						console.log('已自动选择当前在用卡号:', currentCard.cardNo);
					} else if (this.etcCardOptions.length > 0) {
						// 如果没有找到当前在用卡，选择第一张可用卡
						this.selectedEtcCards = [this.etcCardOptions[0]];
						console.log('未找到当前在用卡，已选择第一张可用卡:', this.etcCardOptions[0].cardNo);
					} else {
						// 如果没有任何可用卡，保持空数组
						this.selectedEtcCards = [];
						console.warn('没有可用的ETC卡选项');
					}
				} else {
					this.selectedEtcCards = selectedCards;
				}
				this.updateFormData();
			},
			

			
			// 交易场景选择变化事件
			onSceneChange(selectedScenes) {
				this.selectedScenes = selectedScenes;
				this.updateFormData();
			},
			
			// 更新表单数据
			updateFormData() {
				try {
					// 更新ETC卡号数组
					this.formData.etcCardNos = this.selectedEtcCards.map(card => card.cardNo || card.value);

					// 按ETC卡查询时，车牌号从ETC卡信息中获取
					if (this.queryMode === 'etcCard') {
						if (this.primaryEtcCard.vehicleCode) {
							this.formData.carNos = [{
								vehicleCode: this.primaryEtcCard.vehicleCode,
								vehicleColor: this.primaryEtcCard.vehicleColor || '0'
							}];
						} else {
							// 如果没有关联车辆信息，设置空数组
							this.formData.carNos = [];
						}
					}

					// 更新交易场景数组
					this.formData.scenes = this.selectedScenes.map(scene => scene.value).filter(value => value !== '');

					console.log('更新后的表单数据:', this.formData);
				} catch (error) {
					console.error('更新表单数据时发生错误:', error);
				}
			},
			upper: function(e) {

			},
			scrolltolower: function(e) {
				if (this.flag || this.isLoading) return;
				let self = this;
				setTimeout(function() {
					// 再次检查loading状态，防止在延时期间有其他请求
					if (self.isLoading) return;
					self.formData.pageNum = self.formData.pageNum + 1;
					self.getVehicleConsume();
				}, 500)
			},
			scroll: function(e) {
				this.old.scrollTop = e.detail.scrollTop;
			},
			supplementaryPayment() {
				// 使用传参数据构建补缴参数
				let nextData = {};
				
				if (this.queryMode === 'vehicle' && this.primaryVehicle.vehicleCode) {
					nextData = {
						vehicle_code: this.primaryVehicle.vehicleCode,
						vehicle_color: this.primaryVehicle.vehicleColor,
					};
				} else if (this.queryMode === 'etcCard' && this.primaryEtcCard.vehicleCode) {
					nextData = {
						vehicle_code: this.primaryEtcCard.vehicleCode,
						vehicle_color: this.primaryEtcCard.vehicleColor,
					};
				} else {
					uni.showModal({
						title: '提示',
						content: '缺少车辆信息，无法进行补缴',
						showCancel: false
					});
					return;
				}
				
				uni.navigateTo({
					url: '/pagesC/afterPay/recordDetail/recordDetail?nextData=' + encodeURIComponent(JSON
						.stringify(nextData))
				})
				return;
			},
			searchTable() {
				// 防止重复点击
				if (this.isLoading) {
					return;
				}

				this.formData.pageNum = 1;
				this.flag = false;
				this.updateFormData(); // 更新表单数据
				this.getVehicleConsume();
			},
			getVehicleConsume() {
				// 防止重复请求
				if (this.isLoading) {
					return;
				}

				this.isLoading = true;
				this.currentConsumeList = this.formData.pageNum == 1 ? [] : this.currentConsumeList;
				
				// 构建查询参数 - 适配新接口 /consume/list
				let queryData = {
					custMastId: this.getAccountId(), // 用户ID必传
					pageNum: this.formData.pageNum,
					pageSize: this.formData.pageSize,
					strartTime: this.formData.strartTime,
					endTime: this.formData.endTime
				};
				
				// 处理ETC卡号参数 - 用逗号分割
				let cardNos = [];
				if (this.queryMode === 'vehicle') {
					// 按车牌查询模式 - 获取选中的ETC卡号
					if (this.formData.etcCardNos && this.formData.etcCardNos.length > 0) {
						cardNos = this.formData.etcCardNos;
					} else if (this.primaryVehicle.cardNo) {
						cardNos = [this.primaryVehicle.cardNo];
					}
				} else if (this.queryMode === 'etcCard') {
					// 按ETC卡查询模式 - 使用主要ETC卡号
					if (this.primaryEtcCard.cardNo) {
						cardNos = [this.primaryEtcCard.cardNo];
					}
				}
				queryData.cardNo = cardNos.join(',');
				
				// 处理车牌号参数 - 用逗号分割
				let carNos = [];
				if (this.queryMode === 'vehicle') {
					// 按车牌查询模式 - 使用主要车牌
					if (this.primaryVehicle.vehicleCode) {
						carNos = [this.primaryVehicle.vehicleCode];
					}
				} else if (this.queryMode === 'etcCard') {
					// 按ETC卡查询模式 - 从关联车辆中获取车牌
					console.log('ETC卡模式 - formData.carNos:', this.formData.carNos);
					console.log('ETC卡模式 - primaryEtcCard:', this.primaryEtcCard);
					if (this.formData.carNos && this.formData.carNos.length > 0) {
						carNos = this.formData.carNos.map(car => car.vehicleCode);
					}
				}
				console.log('构建的carNos数组:', carNos);

				// 只有在有车牌号的情况下才添加carNo参数
				if (carNos.length > 0) {
					queryData.carNo = carNos.join(',');
				}
				
				// 处理车牌颜色参数 - 只有在有车牌号的情况下才设置
				if (carNos.length > 0) {
					if (this.queryMode === 'vehicle' && this.primaryVehicle.vehicleColor) {
						queryData.carColor = this.primaryVehicle.vehicleColor;
					} else if (this.queryMode === 'etcCard') {
						if (this.formData.carNos && this.formData.carNos.length > 0 && this.formData.carNos[0].vehicleColor) {
							queryData.carColor = this.formData.carNos[0].vehicleColor;
						} else {
							// ETC卡模式下如果没有车牌颜色信息，使用默认值
							queryData.carColor = '0'; // 默认蓝牌
						}
					}
				}
				
				// 处理交易场景参数 - 用逗号分割
				if (this.formData.scenes && this.formData.scenes.length > 0) {
					queryData.scene = this.formData.scenes.join(',');
				} else {
					queryData.scene = '';
				}
				
				console.log('查询参数:', queryData);
				
				this.$request.post(this.$interfaces.getConsumeList, {
					data: queryData
				}).then(res => {
					console.log('接口返回的完整数据:', res);
					console.log('res.data的结构:', res.data);
					let data = res.data.data
					if (res.code == 200) {
						// 数据结构确认：还是consumeViewList结构
						console.log('consumeViewList:', data.consumeViewList);
						console.log('records数组:', data.consumeViewList.records);
						console.log('total:', data.consumeViewList.total);
						console.log('totalAmount:', data.totalAmount);

						if (this.formData.pageNum == 1) {
							this.totalCount = data.consumeViewList.total || 0;
							this.totalAmount = data.totalAmount || 0;
						}
						let list = data.consumeViewList.records || [];
						console.log('处理前的list:', list);

						list.forEach((item, index) => {
							console.log('处理item:', item);

							if (item.scene == 2) { //停车场	
								console.log('处理停车场数据:', item.vehicleParkingView);
								if (item.vehicleParkingView) {
									for (let key in item.vehicleParkingView) {
										if (key != 'scene') {
											item[key] = item.vehicleParkingView[key]
										}
									}
								}
							}
							if (item.scene == 3) { //加油站
								console.log('处理加油站数据:', item.vehicleStationView);
								if (item.vehicleStationView) {
									for (let key in item.vehicleStationView) {
										if (key != 'scene') {
											item[key] = item.vehicleStationView[key]
										}
									}
								}
							}
							if (item.scene == 1) { //高速
								console.log('处理高速数据:', item.vehiclePassView);
								if (item.vehiclePassView) {
									for (let key in item.vehiclePassView) {
										if (key != 'scene') {
											item[key] = item.vehiclePassView[key]
										}
									}
								}
							}

							console.log('处理后的item:', item);
						})

						console.log('处理后的list:', list);

						this.currentConsumeList = this.currentConsumeList.concat(list)
						if (this.currentConsumeList.length == data.consumeViewList.total) {
							this.flag = true
						}
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}

					// 请求完成，重置loading状态
					this.isLoading = false;

				}).catch(err => {
					// 请求失败，重置loading状态
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: err.msg,
						showCancel: false,
					});
				})
			},
			goDetail(item) {
				console.log('item', item)
				uni.navigateTo({
					url: '/pagesB/cardBusiness/expenseRecord/record-detail?id=' + item.transactionId
				})
			},
			startDateChange(e) {
				this.strartTime = e.detail.value
				this.formData.strartTime = e.detail.value
			},
			endDateChange(e) {
				this.endTime = e.detail.value
				this.startDate = dayjs(new Date(this.endTime).getTime() - 365 * 24 * 60 * 60 * 1000).format('YYYY-MM')

				//如果选择结束日期时，结束日期小于开始日期，默认开始日期为前一个月。否则如果开始日期大于结束日期的前一年，默认开始日期为结束日期前一年
				this.strartTime = (this.endTime > this.strartTime) ? (this.startDate > this.strartTime ? this.startDate :
					this.strartTime) : dayjs(new Date(this.endTime).getTime() - 30 * 24 * 60 * 60 * 1000).format(
					'YYYY-MM')
				this.formData.endTime = e.detail.value
			},
			filterDict(val) {
				let payStatusObj = {
					"0": '扣款中',
					"1": '扣款成功',
					"2": '扣款失败',
					"3": '待补缴',
					"4": '补缴成功',
					"9": '月底统一扣款'
				}
				return payStatusObj[val] || ''
			},
			moneyFilter(val) {
				let value = val;
				if (value == 0) return value;
				value = value / 100;
				return this.toDecimal2(value);
			},
			toDecimal2(x) {
				var f = parseFloat(x);
				if (isNaN(f)) {
					return false;
				}
				var f = Math.round(x * 100) / 100;
				var s = f.toString();
				var rs = s.indexOf(".");
				if (rs < 0) {
					rs = s.length;
					s += ".";
				}
				while (s.length <= rs + 2) {
					s += "0";
				}
				return s;
			},

		},
		destroyed() {

		}
	};
</script>
<style lang="scss" scoped>
	.sellPay-container {
		// height: calc(100vh - var(--safe-area-inset-bottom));
		width: 100%;
		height: 100%;
		position: relative;

		.search {
			background-color: #FFFFFF;
			
			.search-form-group {
				margin: 0 30rpx;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #E9E9E9;
				display: flex;
				align-items: center;
				
				.title {
					width: 230rpx;
					font-size: 30rpx;
					color: #888888;
					font-weight: 400;
				}
				
				.value {
					flex: 1;
					font-size: 30rpx;
					color: #333333;
				}
				
				&.highlight-info {
					background: #E6F3FF;
					margin: 0;
					padding: 24rpx 30rpx;
					border-radius: 0;
					border-bottom: 2rpx solid #E9E9E9;
					.value {
						color: #0066E9;
						font-weight: 600;
					}
				}
			}

			.search-btn {
				display: flex;
				justify-content: center;
				justify-content: space-around;
				padding-top: 17rpx;
				padding-bottom: 22rpx;

				/deep/.cu-btn {
					width: 690rpx;
					height: 84rpx;
					background: #0066E9;
					border-radius: 10rpx;
					font-size: 34rpx;
					font-weight: 500;
					color: #FFFFFF;
					line-height: 26rpx;
				}
			}

			.search-form-item {
				margin: 0 30rpx;
				border-bottom: 1rpx solid #E9E9E9;
				line-height: 80rpx;
				height: 80rpx;
				position: relative;

				.title {
					color: #888;
					font-size: 30rpx;
					width: 230rpx;
				}

				.picker {
					width: calc(100% - 230rpx);
					display: flex;
					height: 100%;
				}

				/deep/.u-border-bottom::after {
					border-bottom: none;
				}

				.pick-date {
					width: 142rpx;
					display: flex;

					/deep/.u-cell {
						position: relative;
					}

					/deep/.u-cell__value {
						font-size: 30rpx !important;
					}

					/deep/.u-cell__left-icon-wrap {
						position: absolute;
						right: 0;
						margin-right: 0px !important;
					}

					/deep/.u-icon__icon {
						font-size: 25rpx !important;
						color: #999999;
					}
				}

				.pick-date-two {
					// flex: 1;
				}

				/deep/.u-cell {
					padding: 0 0;
					line-height: 80rpx;
				}

				/deep/.u-cell__value {
					color: #333;
					text-align: left;
					font-size: 30rpx;
				}
			}
		}

		.card-Info {
			// margin-top: 20rpx;
			padding-bottom: 22rpx;

			.card {
				margin: 0 32rpx 21rpx 32rpx;
				border-radius: 10rpx;
				height: 100%;
				background: #FFFFFF;
				border-radius: 10rpx;
				padding: 25rpx 25rpx 22rpx;

				.card-top {
					display: flex;
					line-height: 56rpx;
					margin-bottom: 26rpx;

					.icon {
						width: 56rpx;
						height: 56rpx;
						background-size: 100%;
					}

					.deduction {
						margin-left: 12rpx;
						height: 28rpx;
						font-size: 32rpx;
						font-weight: 500;
						color: #333333;
						flex: 1;

						.unit {
							color: #EA3030;
							font-size: 24rpx;
							margin-left: 7rpx;
						}

						.money {
							color: #EA3030;
						}
					}
				}
				
				.card-etc-info {
					margin-bottom: 20rpx;
					
					.etc-card-no {
						font-size: 26rpx;
					}

					.status {
						min-width: 104rpx;
						padding: 0 9px;
						height: 44rpx;
						background: rgba(56, 116, 255, 0.1);
						border-radius: 7rpx;
						text-align: center;
						line-height: 44rpx;

						.name {
							color: #3874FF;
							font-size: 26rpx;
							font-weight: 400;
						}
					}

					.status0 {
						background: rgba(56, 116, 255, 0.1);

						.name0 {
							color: #3874FF;
						}
					}

					.status1,
					.statu4 {
						background: rgba(43, 166, 80, 0.1);

						.name1,
						.name4 {
							color: #2BA650;
						}
					}

					.status2 {
						background: rgba(255, 84, 84, 0.1);

						.name2 {
							color: #FF5454;
						}
					}

					.status3,
					.status9 {
						background: rgba(106, 105, 105, 0.1);

						.name3,
						.name9 {
							color: #6A6969;
						}
					}
				}

				.card-middle {
					display: flex;
					width: 100%;
					height: 37rpx;
					font-size: 26rpx;
					font-weight: 400;
					color: #666666;
					line-height: 37rpx;
					margin-bottom: 26rpx;
				}

				.dash-line {
					background-image: linear-gradient(to right, #c3c3c3 0%, #c3c3c3 50%, transparent 50%);
					background-size: 9px 1px;
					background-repeat: repeat-x;
					width: 100%;
					height: 1px;
				}

				.card-btn {
					width: 100%;
					margin-top: 22rpx;
					display: flex;
					justify-content: space-around;
					position: relative;

					.btn {
						width: 168rpx;
						border-radius: 30rpx;
						height: 50rpx;
						border: 1px solid #C6C6C6;
						font-size: 26rpx;
						font-weight: 400;
						color: #999999;
						line-height: 50rpx;
						text-align: center;
					}

					.btn1 {
						border: 1px solid #0066E9;
						font-weight: 400;
						color: #0066E9;
						position: absolute;
						right: 0;
					}
				}
			}
		}

		.no-data {
			width: calc(100% - 60rpx);
			text-align: center;
			background: #fff;
			margin-left: 30rpx;
			border-radius: 10rpx;
			height: 264rpx;

			.no-data-img {
				width: 135rpx;
				height: 178rpx;
				background-size: 100%;
				margin-top: 26rpx;
			}

			.no-data-title {
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				line-height: 40rpx;
			}
		}

		.total {
			width: 100%;
			display: flex;
			padding: 20rpx 30rpx;

			.total-recharge {
				font-size: 30rpx;
				flex: 1;

				span {
					color: #EA3030;
				}
			}

			.frequency {
				font-size: 30rpx;

				span {
					color: #EA3030;
				}
			}
		}
	}
</style>